<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Chiế<PERSON> Game "Moothew"</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Be+Vietnam+Pro:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Chosen Palette: Warm Neutrals & Subtle Teal -->
    <!-- Application Structure Plan: A single-page dashboard with sticky side navigation. The structure is designed for strategic decision-making, not to mirror the report. It starts with a high-level market overview (The "Why"), decodes the trend, presents game concepts in an interactive comparison tool (The "How"), details the operational strategies, and concludes with a recommendation. This flow guides the user from opportunity to execution, making complex information digestible and actionable. -->
    <!-- Visualization & Content Choices: 
        - Market Stats -> Goal: Inform/Impress -> Viz: Animated number counters & Donut Chart (Chart.js) -> Interaction: Hover for details. Justification: Quickly conveys the scale of the opportunity.
        - Trend Elements -> Goal: Explain -> Viz: Interactive cards (HTML/CSS) -> Interaction: Hover effect. Justification: Breaks down a concept into simple, memorable parts.
        - Game Concepts -> Goal: Compare/Analyze -> Viz: Tabbed interface with dynamic text, icons, and a radar chart (Chart.js) -> Interaction: Clicking tabs updates all content. Justification: The core interactive tool. A radar chart is perfect for comparing multiple attributes (cost, potential, risk) of different concepts simultaneously.
        - Strategies -> Goal: Organize/Detail -> Viz: Accordion layout (HTML/CSS/JS) -> Interaction: Click to expand. Justification: Manages a large amount of detailed text without cluttering the UI.
        - Conclusion -> Goal: Recommend -> Viz: Styled recommendation card. Justification: Clearly highlights the final strategic advice.
    -->
    <!-- CONFIRMATION: NO SVG graphics used. NO Mermaid JS used. -->
    <style>
        body {
            font-family: 'Be Vietnam Pro', sans-serif;
            background-color: #FDFBF8;
            color: #333;
        }
        .chart-container {
            position: relative;
            width: 100%;
            max-width: 450px;
            margin-left: auto;
            margin-right: auto;
            height: 300px;
            max-height: 400px;
        }
        @media (min-width: 768px) {
            .chart-container {
                height: 350px;
            }
        }
        .nav-link {
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }
        .nav-link.active {
            background-color: #E6FFFA;
            color: #047857;
            border-left-color: #047857;
        }
        .concept-btn {
            transition: all 0.3s ease;
        }
        .concept-btn.active {
            background-color: #047857;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
        }
        .accordion-content {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.5s ease-out;
        }
        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.05);
            border: 1px solid #F0F0F0;
        }
    </style>
</head>
<body class="antialiased">

    <div class="min-h-screen lg:flex">
        <!-- Sidebar Navigation -->
        <div class="lg:w-64 bg-white/80 backdrop-blur-sm border-r border-gray-200/80 lg:sticky top-0 h-screen hidden lg:block p-4">
            <h1 class="text-xl font-bold text-emerald-800 mb-8 text-center">Chiến lược "Moothew"</h1>
            <nav id="desktop-nav" class="flex flex-col space-y-2">
                <a href="#overview" class="nav-link font-medium p-3 rounded-lg">📊 Tổng quan Thị trường</a>
                <a href="#trend" class="nav-link font-medium p-3 rounded-lg">🔍 Giải mã Trend</a>
                <a href="#concepts" class="nav-link font-medium p-3 rounded-lg">💡 Ý tưởng Game</a>
                <a href="#strategy" class="nav-link font-medium p-3 rounded-lg">⚙️ Chiến lược Vận hành</a>
                <a href="#conclusion" class="nav-link font-medium p-3 rounded-lg">🏆 Đề xuất</a>
            </nav>
        </div>

        <!-- Main Content -->
        <main class="flex-1 p-4 sm:p-6 md:p-10">
            <!-- Header for Mobile -->
            <header class="lg:hidden mb-6">
                <h1 class="text-2xl font-bold text-emerald-800 text-center">Chiến lược "Moothew"</h1>
                 <div class="border-b-2 border-emerald-100 mt-2"></div>
            </header>

            <!-- Section 1: Overview -->
            <section id="overview" class="mb-16 scroll-mt-20">
                <h2 class="text-3xl font-bold text-gray-800 mb-2">Tổng quan Thị trường Game Việt Nam</h2>
                <p class="text-gray-600 mb-8 max-w-3xl">
                    Thị trường game mobile Việt Nam là một "mỏ vàng" kỹ thuật số với tốc độ tăng trưởng bùng nổ và một thế hệ game thủ Gen Z đông đảo, sẵn sàng chi tiêu. Đây là bối cảnh hoàn hảo để một tựa game bắt trend có thể thành công.
                </p>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div class="stat-card text-center">
                        <h3 class="text-lg font-semibold text-gray-500">Doanh thu dự kiến (2029)</h3>
                        <p class="text-4xl font-bold text-emerald-600 my-2">$<span id="revenue-stat">0</span> Tỷ</p>
                        <p class="text-sm text-gray-500">Tăng trưởng 9.77% mỗi năm</p>
                    </div>
                    <div class="stat-card text-center">
                        <h3 class="text-lg font-semibold text-gray-500">Số người chơi Game</h3>
                        <p class="text-4xl font-bold text-emerald-600 my-2"><span id="players-stat">0</span> Triệu</p>
                        <p class="text-sm text-gray-500">Top đầu Đông Nam Á</p>
                    </div>
                    <div class="stat-card text-center">
                        <h3 class="text-lg font-semibold text-gray-500">Lượt tải Game (2023)</h3>
                        <p class="text-4xl font-bold text-emerald-600 my-2"><span id="downloads-stat">0</span> Tỷ</p>
                        <p class="text-sm text-gray-500">Top 5 thế giới về sản xuất</p>
                    </div>
                </div>
                <div class="grid grid-cols-1 lg:grid-cols-5 gap-6 mt-8">
                    <div class="lg:col-span-2 stat-card">
                        <h3 class="text-xl font-bold text-gray-800 mb-4 text-center">Chân dung Game thủ Gen Z</h3>
                        <ul class="space-y-4 text-gray-700">
                            <li class="flex items-start"><span class="text-emerald-500 mr-3 mt-1">✓</span><div><strong class="font-semibold">77%</strong> là người chơi game di động.</div></li>
                            <li class="flex items-start"><span class="text-emerald-500 mr-3 mt-1">✓</span><div><strong class="font-semibold">62%</strong> thường xuyên mua vật phẩm trong game (IAP).</div></li>
                            <li class="flex items-start"><span class="text-emerald-500 mr-3 mt-1">✓</span><div><strong class="font-semibold">17%</strong> nạp tiền vào game <strong class="text-red-500">hàng ngày</strong>.</div></li>
                            <li class="flex items-start"><span class="text-emerald-500 mr-3 mt-1">✓</span><div>Sẵn sàng chi tiêu cho vật phẩm và tính năng xã hội.</div></li>
                        </ul>
                    </div>
                    <div class="lg:col-span-3 stat-card">
                         <h3 class="text-xl font-bold text-gray-800 mb-2 text-center">Thể loại Game ưa thích (Lượt tải)</h3>
                        <div class="chart-container">
                            <canvas id="genreChart"></canvas>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Section 2: Trend -->
            <section id="trend" class="mb-16 scroll-mt-20">
                <h2 class="text-3xl font-bold text-gray-800 mb-2">Giải mã Hiện tượng "Moothew"</h2>
                <p class="text-gray-600 mb-8 max-w-3xl">
                    "Moothew" không chỉ là một meme, mà là một "liều thuốc Dopamine kỹ thuật số" được tạo nên từ sự cộng hưởng của ba yếu tố cốt lõi, đáp ứng đúng nhu cầu giải trí và giải tỏa cảm xúc của Gen Z.
                </p>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="stat-card text-center transform hover:-translate-y-1 transition-transform duration-300">
                        <div class="text-4xl mb-3">🎵</div>
                        <h3 class="text-xl font-bold text-gray-800 mb-2">Âm thanh (Audio)</h3>
                        <p class="text-gray-600">Tiếng "moo" được biến tấu thành "moothew" - một giai điệu "earworm" ngắn, dễ thương, dễ nhớ và gây nghiện.</p>
                    </div>
                    <div class="stat-card text-center transform hover:-translate-y-1 transition-transform duration-300">
                        <div class="text-4xl mb-3">🖼️</div>
                        <h3 class="text-xl font-bold text-gray-800 mb-2">Hình ảnh (Visual)</h3>
                        <p class="text-gray-600">Hình ảnh bò con lông xù, mắt to tròn kích hoạt "phản ứng dễ thương", tạo cảm giác ấm áp, trong sáng.</p>
                    </div>
                    <div class="stat-card text-center transform hover:-translate-y-1 transition-transform duration-300">
                        <div class="text-4xl mb-3">❤️</div>
                        <h3 class="text-xl font-bold text-gray-800 mb-2">Cảm xúc (Emotion)</h3>
                        <p class="text-gray-600">Sự kết hợp hoàn hảo tạo ra nội dung "feel-good", giúp giảm căng thẳng, mang lại niềm vui tức thì.</p>
                    </div>
                </div>
            </section>

            <!-- Section 3: Concepts -->
            <section id="concepts" class="mb-16 scroll-mt-20">
                <h2 class="text-3xl font-bold text-gray-800 mb-2">So sánh các Ý tưởng Game</h2>
                <p class="text-gray-600 mb-8 max-w-3xl">
                    Chọn một concept game để xem phân tích chi tiết về lối chơi, chiến lược và tiềm năng. Sử dụng biểu đồ radar để so sánh trực quan các thuộc tính quan trọng.
                </p>
                <div class="flex justify-center space-x-2 md:space-x-4 mb-8">
                    <button class="concept-btn active p-3 px-6 rounded-full font-semibold bg-white shadow-sm border" data-concept="A">Concept A</button>
                    <button class="concept-btn p-3 px-6 rounded-full font-semibold bg-white shadow-sm border" data-concept="B">Concept B</button>
                    <button class="concept-btn p-3 px-6 rounded-full font-semibold bg-white shadow-sm border" data-concept="C">Concept C</button>
                </div>

                <div class="bg-white rounded-xl shadow-lg p-6 md:p-8 grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <div>
                        <h3 id="concept-title" class="text-2xl font-bold text-emerald-700 mb-1"></h3>
                        <p id="concept-subtitle" class="font-medium text-gray-500 mb-4"></p>
                        <p id="concept-description" class="text-gray-700 mb-6"></p>
                        
                        <div class="space-y-4">
                            <div>
                                <h4 class="font-semibold text-gray-800">🎯 Lối chơi cốt lõi:</h4>
                                <p id="concept-gameplay" class="text-gray-600"></p>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-800">💰 Mô hình kiếm tiền:</h4>
                                <p id="concept-monetization" class="text-gray-600"></p>
                            </div>
                             <div>
                                <h4 class="font-semibold text-gray-800">📈 Chiến lược giữ chân:</h4>
                                <p id="concept-retention" class="text-gray-600"></p>
                            </div>
                        </div>
                    </div>
                    <div class="flex flex-col items-center">
                        <h4 class="font-semibold text-gray-800 text-center mb-4">Phân tích Tiềm năng & Rủi ro</h4>
                        <div class="w-full max-w-md mx-auto h-80 md:h-96">
                            <canvas id="conceptRadarChart"></canvas>
                        </div>
                    </div>
                </div>
            </section>
            
            <!-- Section 4: Strategy -->
            <section id="strategy" class="mb-16 scroll-mt-20">
                <h2 class="text-3xl font-bold text-gray-800 mb-2">Khung Chiến lược Vận hành</h2>
                <p class="text-gray-600 mb-8 max-w-3xl">
                    Một sản phẩm xuất sắc cần một chiến lược vận hành thông minh để tối đa hóa doanh thu và giữ chân người chơi trong dài hạn.
                </p>
                <div class="space-y-4">
                    <div class="accordion-item bg-white rounded-lg shadow-sm border border-gray-200">
                        <button class="accordion-header w-full flex justify-between items-center p-5 text-left font-semibold text-lg text-gray-800">
                            <span>💰 Tối đa hóa Doanh thu (Monetization)</span>
                            <span class="accordion-icon transform transition-transform duration-300">▼</span>
                        </button>
                        <div class="accordion-content px-5 pb-5 text-gray-600">
                            <ul class="list-disc list-inside space-y-2">
                                <li><strong>Quảng cáo có thưởng (Rewarded Ads):</strong> Cho người chơi tự nguyện xem quảng cáo để nhận thưởng (hồi sinh, nhân đôi tiền). Hiệu quả nhất và được chấp nhận nhất.</li>
                                <li><strong>Battle Pass / Season Pass:</strong> Mô hình theo mùa với 2 hàng phần thưởng (miễn phí & trả phí). Giữ chân người chơi rất tốt và là động lực chi tiêu chính.</li>
                                <li><strong>Mua hàng trong ứng dụng (IAP):</strong> Bán vật phẩm trang trí (cosmetics), gói tắt quảng cáo, và các gói vật phẩm độc quyền.</li>
                                <li><strong>Hợp tác thương hiệu (Brand Collab):</strong> Hợp tác với các nhãn hàng (sữa, đồ ăn vặt) để tạo sự kiện và vật phẩm trong game, tạo nguồn thu mới.</li>
                            </ul>
                        </div>
                    </div>
                    <div class="accordion-item bg-white rounded-lg shadow-sm border border-gray-200">
                        <button class="accordion-header w-full flex justify-between items-center p-5 text-left font-semibold text-lg text-gray-800">
                            <span>❤️ Giữ chân Người chơi (Retention)</span>
                            <span class="accordion-icon transform transition-transform duration-300">▼</span>
                        </button>
                        <div class="accordion-content px-5 pb-5 text-gray-600">
                           <ul class="list-disc list-inside space-y-2">
                                <li><strong>Sự kiện Giới hạn (Live Ops & FOMO):</strong> Tổ chức sự kiện theo lễ Tết, ra mắt vật phẩm độc quyền trong thời gian ngắn để tạo cảm giác "sợ bỏ lỡ".</li>
                                <li><strong>Tính năng Xã hội:</strong> Bảng xếp hạng, thách đấu bạn bè, hệ thống bang hội để tạo sự cạnh tranh và gắn kết cộng đồng.</li>
                                <li><strong>Tiến trình Dài hạn:</strong> Hệ thống thành tựu, nhiệm vụ hàng ngày/tuần để người chơi luôn có mục tiêu phấn đấu.</li>
                           </ul>
                        </div>
                    </div>
                    <div class="accordion-item bg-white rounded-lg shadow-sm border border-gray-200">
                        <button class="accordion-header w-full flex justify-between items-center p-5 text-left font-semibold text-lg text-gray-800">
                            <span>🚀 Tiếp cận Thị trường (Go-to-Market)</span>
                            <span class="accordion-icon transform transition-transform duration-300">▼</span>
                        </button>
                        <div class="accordion-content px-5 pb-5 text-gray-600">
                            <ul class="list-disc list-inside space-y-2">
                                <li><strong>TikTok là chủ lực:</strong> Hợp tác với KOC/Influencer, chạy quảng cáo TopView, In-Feed Ads và tối ưu hóa chiến dịch theo ROAS.</li>
                                <li><strong>Vòng lặp Lan truyền (Virality):</strong> Tích hợp sâu TikTok API (Login Kit, Share Kit) để người chơi dễ dàng chia sẻ thành tích, tạo ra marketing truyền miệng.</li>
                                <li><strong>Nội dung do người dùng tạo (UGC):</strong> Khuyến khích người chơi tạo video về game (đặc biệt với Concept C) để game tự marketing cho chính nó.</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Section 5: Conclusion -->
            <section id="conclusion" class="scroll-mt-20">
                <h2 class="text-3xl font-bold text-gray-800 mb-2">Đề xuất Chiến lược Cuối cùng</h2>
                <p class="text-gray-600 mb-6 max-w-3xl">
                    Dựa trên phân tích toàn diện, việc lựa chọn một hướng đi cân bằng giữa khả năng tiếp cận thị trường và tiềm năng doanh thu dài hạn là chìa khóa thành công.
                </p>
                <div class="bg-gradient-to-br from-emerald-50 to-green-100 border-l-4 border-emerald-600 rounded-r-lg p-6 shadow-md">
                    <h3 class="text-2xl font-bold text-emerald-800 mb-3">🏆 Đề xuất mạnh mẽ nhất: Concept B - "Nông Trại Giai Điệu"</h3>
                    <p class="text-gray-700 mb-4">
                        Mô hình <strong>Hybrid-casual</strong> này là sự lựa chọn tối ưu. Nó giải quyết được cả hai bài toán lớn của ngành game:
                    </p>
                    <ul class="space-y-2 text-gray-700">
                        <li class="flex items-start"><span class="text-emerald-600 font-bold mr-3 mt-1">1.</span> <div><strong>Thu hút người dùng (Acquisition):</strong> Sử dụng gameplay âm nhạc (rhythm) đơn giản, vui nhộn và hợp trend để làm "phễu" thu hút lượng lớn người chơi Gen Z từ TikTok.</div></li>
                        <li class="flex items-start"><span class="text-emerald-600 font-bold mr-3 mt-1">2.</span> <div><strong>Giữ chân & Kiếm tiền (Retention & Monetization):</strong> Lớp meta-game về xây dựng, trang trí nông trại và sưu tầm sẽ giữ chân người chơi lâu dài, tạo cơ hội tối đa hóa doanh thu qua Battle Pass và IAP.</div></li>
                    </ul>
                    <p class="mt-4 font-semibold text-emerald-700">
                        Đây là chiến lược bền vững, phù hợp nhất với đặc điểm thị trường Việt Nam, có khả năng xây dựng một sản phẩm thành công cả về mặt thương mại lẫn cộng đồng.
                    </p>
                </div>
            </section>
        </main>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const data = {
                stats: {
                    revenue: 2.42,
                    players: 54.6,
                    downloads: 4.2,
                },
                genres: {
                    labels: ['Hyper-casual', 'Mô phỏng', 'Hành động', 'Khác'],
                    data: [32.49, 15.82, 11.76, 39.93]
                },
                concepts: {
                    A: {
                        title: 'Concept A: "Đồng Cỏ Moothew"',
                        subtitle: 'Hyper-casual Endless Runner',
                        description: 'Tiếp cận trực diện và nhanh nhất, nhằm chuyển hóa sức nóng của trend thành lượt tải tối đa. Chiến lược "đánh nhanh thắng nhanh", phù hợp cho việc thâm nhập thị trường tốc độ.',
                        gameplay: 'Người chơi điều khiển bò "Moothew" chạy vô tận, nhảy qua chướng ngại vật bằng một chạm. Thu thập vật phẩm để mở khóa hàng chục skin khác nhau.',
                        monetization: 'Chủ yếu từ Quảng cáo (Rewarded & Interstitial). Cung cấp gói IAP "Tắt quảng cáo vĩnh viễn" để tối ưu doanh thu.',
                        retention: 'Gameplay gây nghiện, bảng xếp hạng điểm cao, và hệ thống sưu tầm skin đa dạng để khuyến khích chơi lại.',
                        radarData: [4, 2, 5, 2, 4] // [Phát triển, Doanh thu, Lan truyền, Giữ chân, Rủi ro]
                    },
                    B: {
                        title: 'Concept B: "Nông Trại Giai Điệu"',
                        subtitle: 'Hybrid-casual Rhythm & Simulation',
                        description: 'Mô hình "Trend-to-Hybrid-Casual" cân bằng và bền vững, kết hợp gameplay gây nghiện và meta-game có chiều sâu để tối đa hóa vòng đời sản phẩm và doanh thu.',
                        gameplay: 'Chơi game âm nhạc để kiếm tài nguyên, sau đó dùng tài nguyên để xây dựng, trang trí nông trại và sưu tầm các "nhạc công" động vật độc đáo.',
                        monetization: 'Battle Pass là động lực chính, kết hợp cửa hàng bán vật phẩm trang trí (IAP) và quảng cáo tự nguyện (opt-in).',
                        retention: 'Sự kiện theo mùa (Live Ops), meta-game xây dựng & sưu tầm có chiều sâu, và các tính năng xã hội như bang hội.',
                        radarData: [3, 4, 4, 5, 3]
                    },
                    C: {
                        title: 'Concept C: "Thú cưng Moothew AR"',
                        subtitle: 'AR Virtual Pet & UGC Platform',
                        description: 'Concept tham vọng nhất, sử dụng công nghệ AR để tạo trải nghiệm đột phá và xây dựng một nền tảng cộng đồng vững mạnh, hướng đến việc dẫn đầu xu hướng.',
                        gameplay: 'Chăm sóc thú cưng "Moothew" ảo trong thế giới thực qua camera. Tạo và chia sẻ các video ngắn, hài hước trực tiếp lên TikTok.',
                        monetization: 'Tập trung vào IAP bán vật phẩm cá nhân hóa (trang phục, phụ kiện). Tiềm năng lớn từ hợp tác thương hiệu.',
                        retention: 'Vòng lặp chăm sóc thú cưng, cơ chế sáng tạo UGC mạnh mẽ, và các cuộc thi cộng đồng để khuyến khích người chơi tạo nội dung.',
                        radarData: [2, 5, 3, 4, 5]
                    }
                }
            };

            function animateValue(obj, start, end, duration) {
                let startTimestamp = null;
                const step = (timestamp) => {
                    if (!startTimestamp) startTimestamp = timestamp;
                    const progress = Math.min((timestamp - startTimestamp) / duration, 1);
                    obj.innerHTML = (progress * (end - start) + start).toFixed(2).replace('.00', '');
                    if (progress < 1) {
                        window.requestAnimationFrame(step);
                    }
                };
                window.requestAnimationFrame(step);
            }

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const el = entry.target;
                        if (el.id === 'revenue-stat') animateValue(el, 0, data.stats.revenue, 1500);
                        if (el.id === 'players-stat') animateValue(el, 0, data.stats.players, 1500);
                        if (el.id === 'downloads-stat') animateValue(el, 0, data.stats.downloads, 1500);
                        observer.unobserve(el);
                    }
                });
            }, { threshold: 0.5 });
            
            observer.observe(document.getElementById('revenue-stat'));
            observer.observe(document.getElementById('players-stat'));
            observer.observe(document.getElementById('downloads-stat'));


            const genreCtx = document.getElementById('genreChart').getContext('2d');
            new Chart(genreCtx, {
                type: 'doughnut',
                data: {
                    labels: data.genres.labels,
                    datasets: [{
                        label: 'Lượt tải',
                        data: data.genres.data,
                        backgroundColor: [
                            '#34D399',
                            '#60A5FA',
                            '#F87171',
                            '#A78BFA'
                        ],
                        borderColor: '#FDFBF8',
                        borderWidth: 4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                font: {
                                    family: "'Be Vietnam Pro', sans-serif"
                                }
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    let label = context.label || '';
                                    if (label) {
                                        label += ': ';
                                    }
                                    if (context.parsed !== null) {
                                        label += context.parsed + '%';
                                    }
                                    return label;
                                }
                            }
                        }
                    }
                }
            });

            const radarCtx = document.getElementById('conceptRadarChart').getContext('2d');
            const radarChart = new Chart(radarCtx, {
                type: 'radar',
                data: {
                    labels: ['Chi phí P.triển', 'Tiềm năng D.thu', 'Khả năng L.truyền', 'Giữ chân L.dài', 'Mức độ Rủi ro'],
                    datasets: [{
                        label: 'Concept A',
                        data: data.concepts.A.radarData,
                        fill: true,
                        backgroundColor: 'rgba(52, 211, 153, 0.2)',
                        borderColor: 'rgb(52, 211, 153)',
                        pointBackgroundColor: 'rgb(52, 211, 153)',
                        pointBorderColor: '#fff',
                        pointHoverBackgroundColor: '#fff',
                        pointHoverBorderColor: 'rgb(52, 211, 153)'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        r: {
                            angleLines: {
                                color: 'rgba(0, 0, 0, 0.1)'
                            },
                            grid: {
                                color: 'rgba(0, 0, 0, 0.1)'
                            },
                            pointLabels: {
                                font: {
                                    size: 12,
                                    family: "'Be Vietnam Pro', sans-serif"
                                }
                            },
                            ticks: {
                                backdropColor: 'transparent',
                                stepSize: 1,
                                max: 5,
                                min: 1,
                                display: false
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        }
                    }
                }
            });

            const conceptButtons = document.querySelectorAll('.concept-btn');
            const conceptTitle = document.getElementById('concept-title');
            const conceptSubtitle = document.getElementById('concept-subtitle');
            const conceptDescription = document.getElementById('concept-description');
            const conceptGameplay = document.getElementById('concept-gameplay');
            const conceptMonetization = document.getElementById('concept-monetization');
            const conceptRetention = document.getElementById('concept-retention');

            function updateConceptView(conceptKey) {
                const concept = data.concepts[conceptKey];
                conceptTitle.textContent = concept.title;
                conceptSubtitle.textContent = concept.subtitle;
                conceptDescription.textContent = concept.description;
                conceptGameplay.textContent = concept.gameplay;
                conceptMonetization.textContent = concept.monetization;
                conceptRetention.textContent = concept.retention;

                radarChart.data.datasets[0].data = concept.radarData;
                radarChart.data.datasets[0].label = `Concept ${conceptKey}`;
                radarChart.update();

                conceptButtons.forEach(btn => {
                    btn.classList.toggle('active', btn.dataset.concept === conceptKey);
                });
            }

            conceptButtons.forEach(button => {
                button.addEventListener('click', () => {
                    updateConceptView(button.dataset.concept);
                });
            });

            updateConceptView('A');

            const accordionHeaders = document.querySelectorAll('.accordion-header');
            accordionHeaders.forEach(header => {
                header.addEventListener('click', () => {
                    const content = header.nextElementSibling;
                    const icon = header.querySelector('.accordion-icon');
                    
                    if (content.style.maxHeight) {
                        content.style.maxHeight = null;
                        icon.style.transform = 'rotate(0deg)';
                    } else {
                        document.querySelectorAll('.accordion-content').forEach(c => c.style.maxHeight = null);
                        document.querySelectorAll('.accordion-icon').forEach(i => i.style.transform = 'rotate(0deg)');
                        content.style.maxHeight = content.scrollHeight + "px";
                        icon.style.transform = 'rotate(180deg)';
                    }
                });
            });

            const navLinks = document.querySelectorAll('.nav-link');
            const sections = document.querySelectorAll('section');

            const onScroll = () => {
                let current = '';
                sections.forEach(section => {
                    const sectionTop = section.offsetTop;
                    if (pageYOffset >= sectionTop - 100) {
                        current = section.getAttribute('id');
                    }
                });

                navLinks.forEach(link => {
                    link.classList.remove('active');
                    if (link.getAttribute('href').includes(current)) {
                        link.classList.add('active');
                    }
                });
            };

            window.addEventListener('scroll', onScroll);
            onScroll();
            
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    document.querySelector(this.getAttribute('href')).scrollIntoView({
                        behavior: 'smooth'
                    });
                });
            });

        });
    </script>
</body>
</html>
