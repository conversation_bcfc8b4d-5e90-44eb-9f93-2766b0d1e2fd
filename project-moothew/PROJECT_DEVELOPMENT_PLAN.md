# Project Moothew - Development Plan
## Moo Farm: Music & Build - Nông Trại Âm Nhạc

### 🎯 **Executive Summary**
Phát triển game Hybrid-casual kết hợp Rhythm Game và Farm Simulation bằng SwiftUI + SpriteKit, nhắm đến thị trường Gen Z Việt Nam với mô hình "Trend-to-Hybrid-Casual".

---

## 📋 **Project Overview**

### **Game Concept**
- **Core Loop 1:** Rhythm Game - Chạm đúng nhịp các nốt nhạc hình động vật để kiếm tài nguyên
- **Core Loop 2:** Farm Simulation - X<PERSON><PERSON> dựng, trang trí nông trại và sưu tầm "nhạc công" động vật
- **Meta Game:** Battle Pass, Live Ops, Social Features

### **Tech Stack**
- **UI Framework:** SwiftUI (iOS 17+)
- **Game Engine:** SpriteKit (cho Rhythm Game scenes)
- **Architecture:** MVVM + Combine
- **Data:** Core Data (player progress, farm data)
- **Audio:** AVFoundation (music playback, sound effects)
- **Social:** GameKit (leaderboards, achievements)
- **Development:** Xcode 15+

---

## 🗓️ **Development Roadmap (16 Weeks)**

### **Phase 1: Foundation & Architecture (Weeks 1-3)**

#### **Week 1: Project Setup & Architecture**
- [ ] Xcode project setup với SwiftUI + SpriteKit template
- [ ] Implement MVVM architecture với Combine
- [ ] Setup Core Data stack với basic entities
- [ ] Create project structure theo feature-based organization
- [ ] Setup SwiftLint configuration
- [ ] Git repository setup với GitFlow branching

#### **Week 2: Core Data Models & Services**
- [ ] Design và implement Core Data entities:
  - `Player` (level, coins, gems, experience)
  - `Farm` (layout, decorations, buildings)
  - `Animal` (type, level, unlock status)
  - `Song` (difficulty, high score, unlock status)
- [ ] Create service layer: `DataService`, `AudioService`, `GameKitService`
- [ ] Implement basic persistence operations

#### **Week 3: Basic UI Framework**
- [ ] Create SwiftUI navigation structure
- [ ] Implement base ViewModels với Combine publishers
- [ ] Design system: Colors, Fonts, Components
- [ ] Basic screens: Main Menu, Settings, Loading
- [ ] Setup AVFoundation cho audio playback

### **Phase 2: Rhythm Game Core (Weeks 4-7)**

#### **Week 4: SpriteKit Scene Setup**
- [ ] Create `RhythmGameScene` với SpriteKit
- [ ] Implement basic note spawning system
- [ ] Setup touch detection và scoring
- [ ] Create animal-shaped note sprites
- [ ] Basic "moothew" sound integration

#### **Week 5: Rhythm Game Mechanics**
- [ ] Implement timing system với beat detection
- [ ] Create combo system và score multipliers
- [ ] Add visual feedback (particles, animations)
- [ ] Implement difficulty scaling
- [ ] Perfect/Good/Miss hit detection

#### **Week 6: Audio Integration**
- [ ] AVFoundation music playback với beat sync
- [ ] Dynamic audio mixing (background + note sounds)
- [ ] Audio latency compensation
- [ ] Sound effect library (animal sounds, UI sounds)
- [ ] Volume controls và audio settings

#### **Week 7: Rhythm Game Polish**
- [ ] Visual polish: animations, transitions
- [ ] Performance optimization
- [ ] Different song difficulties
- [ ] Tutorial system cho rhythm game
- [ ] Basic reward system (coins, experience)

### **Phase 3: Farm Simulation Layer (Weeks 8-11)**

#### **Week 8: Farm UI Foundation**
- [ ] Create farm grid system với SwiftUI
- [ ] Implement drag & drop cho building placement
- [ ] Basic farm buildings: Barn, House, Decorations
- [ ] Resource management UI (coins, materials)
- [ ] Farm save/load functionality

#### **Week 9: Animal Collection System**
- [ ] Animal unlock system tied to rhythm game performance
- [ ] Animal breeding/evolution mechanics
- [ ] Animal care system (feeding, happiness)
- [ ] Collection book UI
- [ ] Rare animal variants

#### **Week 10: Building & Crafting**
- [ ] Building upgrade system
- [ ] Crafting recipes using rhythm game rewards
- [ ] Farm expansion mechanics
- [ ] Decoration system với customization
- [ ] Resource production buildings

#### **Week 11: Farm-Rhythm Integration**
- [ ] Animals generate new songs when collected
- [ ] Farm productivity affects rhythm game bonuses
- [ ] Cross-system progression rewards
- [ ] Daily farm tasks
- [ ] Achievement system integration

### **Phase 4: Monetization & Social (Weeks 12-14)**

#### **Week 12: Battle Pass System**
- [ ] Battle Pass UI với free/premium tracks
- [ ] Seasonal content system
- [ ] Reward distribution logic
- [ ] Progress tracking và notifications
- [ ] Purchase flow integration

#### **Week 13: GameKit Integration**
- [ ] Player authentication
- [ ] Leaderboards (rhythm scores, farm ratings)
- [ ] Achievements system
- [ ] Friend challenges
- [ ] Social sharing features

#### **Week 14: Monetization Features**
- [ ] In-App Purchase setup
- [ ] Cosmetic item store
- [ ] Rewarded ads integration
- [ ] Premium currency system
- [ ] Purchase validation và receipt handling

### **Phase 5: Polish & Launch Prep (Weeks 15-16)**

#### **Week 15: Performance & Testing**
- [ ] Performance optimization (memory, battery)
- [ ] Comprehensive testing suite
- [ ] Device compatibility testing
- [ ] Accessibility features
- [ ] Bug fixes và stability improvements

#### **Week 16: Launch Preparation**
- [ ] App Store assets (screenshots, description)
- [ ] Privacy policy và terms of service
- [ ] Analytics integration
- [ ] Crash reporting setup
- [ ] Final QA và submission preparation

---

## 🏗️ **Technical Architecture**

### **Directory Structure**
```
MooFarm/
├── App/
│   ├── MooFarmApp.swift
│   └── Assets.xcassets
├── Core/
│   ├── Data/
│   │   ├── CoreDataStack.swift
│   │   └── MooFarm.xcdatamodeld
│   ├── Services/
│   │   ├── AudioService.swift
│   │   ├── GameKitService.swift
│   │   └── DataService.swift
│   └── Extensions/
├── Features/
│   ├── RhythmGame/
│   │   ├── Scenes/
│   │   ├── Views/
│   │   └── ViewModels/
│   ├── Farm/
│   │   ├── Views/
│   │   ├── ViewModels/
│   │   └── Models/
│   ├── Menu/
│   └── Settings/
└── Resources/
    ├── Audio/
    ├── Sprites/
    └── Fonts/
```

### **Key Components**
- **RhythmGameScene (SpriteKit):** Core rhythm gameplay
- **FarmView (SwiftUI):** Farm building và management
- **GameViewModel:** Central game state management
- **AudioService:** Music playback và sound effects
- **DataService:** Core Data operations
- **GameKitService:** Social features

---

## 🎨 **Design Specifications**

### **Visual Style**
- **Art Style:** Cute, colorful, 2D cartoon
- **Color Palette:** Warm pastels với accent colors
- **Typography:** Rounded, friendly fonts
- **UI Elements:** Soft shadows, rounded corners

### **Audio Design**
- **Music:** Upbeat, farm-themed melodies
- **SFX:** Animal sounds, UI feedback, ambient farm sounds
- **Voice:** Optional "moothew" samples

---

## 📊 **Success Metrics**

### **Development KPIs**
- Code coverage: >80%
- Build success rate: >95%
- Crash-free sessions: >99.5%

### **Game KPIs**
- Day 1 Retention: >40%
- Day 7 Retention: >20%
- Session length: 5-10 minutes
- ARPU: $0.50-1.00

---

## 🚀 **Next Steps**

1. **Immediate (Week 1):**
   - Setup Xcode project
   - Create GitHub repository
   - Begin architecture implementation

2. **Short-term (Weeks 2-4):**
   - Complete foundation layer
   - Begin rhythm game prototype

3. **Medium-term (Weeks 5-12):**
   - Complete core gameplay loops
   - Integrate monetization features

4. **Long-term (Weeks 13-16):**
   - Polish và launch preparation
   - Marketing campaign preparation

---

**Project Lead:** [Your Name]  
**Start Date:** [Current Date]  
**Target Launch:** [16 weeks from start]  
**Platform:** iOS 17+ (iPhone/iPad)
