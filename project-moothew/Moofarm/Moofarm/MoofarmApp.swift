//
//  MoofarmApp.swift
//  Moofarm
//
//  Created by Project Moothew on 28/7/25.
//

import SwiftUI

@main
struct MoofarmApp: App {

    // MARK: - Services
    let coreDataStack = CoreDataStack.shared
    let dataService = DataService.shared
    let audioService = AudioService.shared
    let gameKitService = GameKitService.shared
    let gameManager = GameManager.shared

    // MARK: - App Lifecycle
    var body: some Scene {
        WindowGroup {
            ContentView()
                .environment(\.managedObjectContext, coreDataStack.viewContext)
                .environmentObject(dataService)
                .environmentObject(audioService)
                .environmentObject(gameKitService)
                .environmentObject(gameManager)
                .onAppear {
                    setupApp()
                }
        }
    }

    // MARK: - Setup
    private func setupApp() {
        // Authenticate with GameKit
        _ = gameKitService.authenticatePlayer()

        // Setup audio session
        // Audio service handles this internally

        // Initialize game manager
        Task {
            await gameManager.initializeGame()
        }

        print("🎮 Moo Farm: Music & Build - App Started")
        print("📱 Project Moothew - Feature-based Architecture Loaded")
    }
}
