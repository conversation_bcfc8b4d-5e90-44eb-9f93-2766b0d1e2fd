<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<model type="com.apple.IDECoreDataModeler.DataModel" documentVersion="1.0" lastSavedToolsVersion="1" systemVersion="11A491" minimumToolsVersion="Automatic" sourceLanguage="Swift" usedWithCloudKit="false" userDefinedModelVersionIdentifier="">

    <!-- Player Entity - Main player data -->
    <entity name="Player" representedClassName="Player" syncable="YES" codeGenerationType="class">
        <attribute name="level" attributeType="Integer 32" defaultValueString="1" usesScalarValueType="YES"/>
        <attribute name="experience" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="coins" attributeType="Integer 32" defaultValueString="100" usesScalarValueType="YES"/>
        <attribute name="gems" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="playerName" optional="YES" attributeType="String"/>
        <attribute name="createdAt" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="lastPlayedAt" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="totalPlayTime" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="battlePassLevel" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="battlePassXP" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="hasPremiumBattlePass" attributeType="Boolean" defaultValueString="NO" usesScalarValueType="YES"/>
        <relationship name="farm" optional="YES" maxCount="1" deletionRule="Cascade" destinationEntity="Farm" inverseName="player" inverseEntity="Farm"/>
        <relationship name="unlockedAnimals" optional="YES" toMany="YES" deletionRule="Cascade" destinationEntity="Animal" inverseName="player" inverseEntity="Animal"/>
        <relationship name="songProgress" optional="YES" toMany="YES" deletionRule="Cascade" destinationEntity="SongProgress" inverseName="player" inverseEntity="SongProgress"/>
    </entity>

    <!-- Animal Entity - Collectible animals -->
    <entity name="Animal" representedClassName="Animal" syncable="YES" codeGenerationType="class">
        <attribute name="type" attributeType="String"/>
        <attribute name="rarity" attributeType="String"/>
        <attribute name="level" attributeType="Integer 32" defaultValueString="1" usesScalarValueType="YES"/>
        <attribute name="happiness" attributeType="Float" defaultValueString="1.0" usesScalarValueType="YES"/>
        <attribute name="soundVariant" attributeType="String"/>
        <attribute name="isUnlocked" attributeType="Boolean" defaultValueString="NO" usesScalarValueType="YES"/>
        <attribute name="unlockedAt" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="lastFedAt" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="totalFeedings" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <relationship name="player" optional="YES" maxCount="1" deletionRule="Nullify" destinationEntity="Player" inverseName="unlockedAnimals" inverseEntity="Player"/>
        <relationship name="songs" optional="YES" toMany="YES" deletionRule="Cascade" destinationEntity="Song" inverseName="sourceAnimal" inverseEntity="Song"/>
        <relationship name="farmAnimals" optional="YES" toMany="YES" deletionRule="Cascade" destinationEntity="FarmAnimal" inverseName="animal" inverseEntity="FarmAnimal"/>
    </entity>

    <!-- Farm Entity - Player's farm -->
    <entity name="Farm" representedClassName="Farm" syncable="YES" codeGenerationType="class">
        <attribute name="farmName" optional="YES" attributeType="String"/>
        <attribute name="level" attributeType="Integer 32" defaultValueString="1" usesScalarValueType="YES"/>
        <attribute name="happiness" attributeType="Float" defaultValueString="0.5" usesScalarValueType="YES"/>
        <attribute name="layoutData" optional="YES" attributeType="Binary"/>
        <attribute name="lastUpdated" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="totalBuildings" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="farmRating" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <relationship name="player" optional="YES" maxCount="1" deletionRule="Nullify" destinationEntity="Player" inverseName="farm" inverseEntity="Player"/>
        <relationship name="buildings" optional="YES" toMany="YES" deletionRule="Cascade" destinationEntity="Building" inverseName="farm" inverseEntity="Building"/>
        <relationship name="farmAnimals" optional="YES" toMany="YES" deletionRule="Cascade" destinationEntity="FarmAnimal" inverseName="farm" inverseEntity="FarmAnimal"/>
    </entity>

    <!-- Song Entity - Rhythm game songs -->
    <entity name="Song" representedClassName="Song" syncable="YES" codeGenerationType="class">
        <attribute name="title" attributeType="String"/>
        <attribute name="difficulty" attributeType="String"/>
        <attribute name="bpm" attributeType="Double" defaultValueString="120.0" usesScalarValueType="YES"/>
        <attribute name="duration" attributeType="Double" defaultValueString="60.0" usesScalarValueType="YES"/>
        <attribute name="fileName" attributeType="String"/>
        <attribute name="isUnlocked" attributeType="Boolean" defaultValueString="NO" usesScalarValueType="YES"/>
        <attribute name="unlockedAt" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="noteData" optional="YES" attributeType="Binary"/>
        <relationship name="sourceAnimal" optional="YES" maxCount="1" deletionRule="Nullify" destinationEntity="Animal" inverseName="songs" inverseEntity="Animal"/>
        <relationship name="progress" optional="YES" toMany="YES" deletionRule="Cascade" destinationEntity="SongProgress" inverseName="song" inverseEntity="SongProgress"/>
    </entity>

    <!-- SongProgress Entity - Player progress on songs -->
    <entity name="SongProgress" representedClassName="SongProgress" syncable="YES" codeGenerationType="class">
        <attribute name="highScore" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="bestCombo" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="timesPlayed" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="isCompleted" attributeType="Boolean" defaultValueString="NO" usesScalarValueType="YES"/>
        <attribute name="completedAt" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="lastPlayedAt" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="perfectHits" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="goodHits" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="okayHits" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="missedHits" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <relationship name="player" optional="YES" maxCount="1" deletionRule="Nullify" destinationEntity="Player" inverseName="songProgress" inverseEntity="Player"/>
        <relationship name="song" optional="YES" maxCount="1" deletionRule="Nullify" destinationEntity="Song" inverseName="progress" inverseEntity="Song"/>
    </entity>

    <!-- Building Entity - Farm buildings -->
    <entity name="Building" representedClassName="Building" syncable="YES" codeGenerationType="class">
        <attribute name="type" attributeType="String"/>
        <attribute name="level" attributeType="Integer 32" defaultValueString="1" usesScalarValueType="YES"/>
        <attribute name="positionX" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="positionY" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="isConstructed" attributeType="Boolean" defaultValueString="YES" usesScalarValueType="YES"/>
        <attribute name="constructedAt" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="lastUpgradedAt" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="productionRate" attributeType="Float" defaultValueString="1.0" usesScalarValueType="YES"/>
        <relationship name="farm" optional="YES" maxCount="1" deletionRule="Nullify" destinationEntity="Farm" inverseName="buildings" inverseEntity="Farm"/>
    </entity>

    <!-- FarmAnimal Entity - Animals placed on farm -->
    <entity name="FarmAnimal" representedClassName="FarmAnimal" syncable="YES" codeGenerationType="class">
        <attribute name="positionX" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="positionY" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="placedAt" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="isActive" attributeType="Boolean" defaultValueString="YES" usesScalarValueType="YES"/>
        <attribute name="lastInteractionAt" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <relationship name="animal" optional="YES" maxCount="1" deletionRule="Nullify" destinationEntity="Animal" inverseName="farmAnimals" inverseEntity="Animal"/>
        <relationship name="farm" optional="YES" maxCount="1" deletionRule="Nullify" destinationEntity="Farm" inverseName="farmAnimals" inverseEntity="Farm"/>
    </entity>

    <elements>
        <element name="Player" positionX="-63" positionY="-18" width="128" height="239"/>
        <element name="Animal" positionX="144" positionY="-18" width="128" height="209"/>
        <element name="Farm" positionX="351" positionY="-18" width="128" height="179"/>
        <element name="Song" positionX="-63" positionY="225" width="128" height="179"/>
        <element name="SongProgress" positionX="144" positionY="225" width="128" height="209"/>
        <element name="Building" positionX="351" positionY="225" width="128" height="164"/>
        <element name="FarmAnimal" positionX="558" positionY="-18" width="128" height="134"/>
    </elements>
</model>