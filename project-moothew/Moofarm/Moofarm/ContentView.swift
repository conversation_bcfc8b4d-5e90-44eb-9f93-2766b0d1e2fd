//
//  ContentView.swift
//  Moofarm
//
//  Created by Project Moothew on 28/7/25.
//

import SwiftUI
import CoreData

/// Main content view that serves as the app's root view
/// Currently delegates to MainMenuView but can be expanded for navigation coordination
struct ContentView: View {

    // MARK: - Environment
    @Environment(\.managedObjectContext) private var viewContext
    @EnvironmentObject private var audioService: AudioService
    @EnvironmentObject private var gameKitService: GameKitService
    @EnvironmentObject private var dataService: DataService

    // MARK: - State
    @State private var currentGameState: GameState = .menu
    @State private var isAppReady = false

    // MARK: - Body
    var body: some View {
        Group {
            if isAppReady {
                // Main app content based on current state
                switch currentGameState {
                case .menu:
                    MainMenuView()
                case .rhythmGame:
                    RhythmGamePlaceholderView()
                case .farm:
                    FarmPlaceholderView()
                case .settings:
                    SettingsView()
                case .loading:
                    LoadingView()
                case .animalCollection:
                    AnimalCollectionView()
                case .battlePass:
                    BattlePassPlaceholderView()
                }
            } else {
                // App initialization screen
                LoadingView()
                    .onAppear {
                        initializeApp()
                    }
            }
        }
        .animation(.easeInOut(duration: 0.3), value: currentGameState)
        .animation(.easeInOut(duration: 0.5), value: isAppReady)
    }

    // MARK: - Initialization
    private func initializeApp() {
        Task {
            // Simulate app initialization
            try? await Task.sleep(nanoseconds: 1_000_000_000) // 1 second

            await MainActor.run {
                isAppReady = true
                print("🎮 Moo Farm initialized successfully")
                print("📱 Feature-based architecture ready")
            }
        }
    }
}

// MARK: - Placeholder Views
struct RhythmGamePlaceholderView: View {
    var body: some View {
        VStack(spacing: 20) {
            Text("🎵")
                .font(.system(size: 80))

            Text("Rhythm Game")
                .font(.largeTitle.bold())

            Text("Coming Soon!")
                .font(.title2)
                .foregroundColor(.secondary)

            Text("This will be the SpriteKit-based rhythm game where players tap animal-shaped notes to the beat.")
                .multilineTextAlignment(.center)
                .padding(.horizontal, 40)
                .foregroundColor(.secondary)

            Spacer()
        }
        .padding()
        .navigationBarHidden(true)
    }
}

struct FarmPlaceholderView: View {
    var body: some View {
        VStack(spacing: 20) {
            Text("🏡")
                .font(.system(size: 80))

            Text("Farm Builder")
                .font(.largeTitle.bold())

            Text("Coming Soon!")
                .font(.title2)
                .foregroundColor(.secondary)

            Text("This will be the SwiftUI-based farm building interface where players can construct and decorate their farm.")
                .multilineTextAlignment(.center)
                .padding(.horizontal, 40)
                .foregroundColor(.secondary)

            Spacer()
        }
        .padding()
        .navigationBarHidden(true)
    }
}

struct BattlePassPlaceholderView: View {
    var body: some View {
        VStack(spacing: 20) {
            Text("🏆")
                .font(.system(size: 80))

            Text("Battle Pass")
                .font(.largeTitle.bold())

            Text("Coming Soon!")
                .font(.title2)
                .foregroundColor(.secondary)

            Text("Seasonal progression system with exclusive rewards and challenges.")
                .multilineTextAlignment(.center)
                .padding(.horizontal, 40)
                .foregroundColor(.secondary)

            Spacer()
        }
        .padding()
        .navigationBarHidden(true)
    }
}

struct LoadingView: View {
    @State private var isAnimating = false

    var body: some View {
        VStack(spacing: 30) {
            // Animated cow emoji
            Text("🐄")
                .font(.system(size: 80))
                .scaleEffect(isAnimating ? 1.2 : 1.0)
                .animation(.easeInOut(duration: 1.0).repeatForever(autoreverses: true), value: isAnimating)

            Text("Moo Farm")
                .font(.system(size: 36, weight: .bold, design: .rounded))

            Text("Loading...")
                .font(.title2)
                .foregroundColor(.secondary)

            ProgressView()
                .scaleEffect(1.5)
                .tint(.blue)
        }
        .onAppear {
            isAnimating = true
        }
    }
}

// MARK: - Preview
#Preview {
    ContentView()
        .environment(\.managedObjectContext, PersistenceController.preview.container.viewContext)
        .environmentObject(AudioService.shared)
        .environmentObject(GameKitService.shared)
        .environmentObject(DataService.shared)
}
