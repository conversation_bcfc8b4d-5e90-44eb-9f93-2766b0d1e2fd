//
//  ContentView.swift
//  Moofarm
//
//  Created by Project Moothew on 28/7/25.
//

import SwiftUI
import CoreData
import Combine

/// Main content view that serves as the app's root view
/// Currently delegates to MainMenuView but can be expanded for navigation coordination
struct ContentView: View {

    // MARK: - Environment
    @Environment(\.managedObjectContext) private var viewContext
    @EnvironmentObject private var audioService: AudioService
    @EnvironmentObject private var gameKitService: GameKitService
    @EnvironmentObject private var dataService: DataService

    // MARK: - State
    @State private var currentGameState: GameState = .menu
    @State private var isAppReady = false

    // MARK: - Body
    var body: some View {
        Group {
            if isAppReady {
                // Main app content based on current state
                switch currentGameState {
                case .menu:
                    MainMenuView()
                case .rhythmGame:
                    RhythmGamePlaceholderView()
                case .farm:
                    FarmPlaceholderView()
                case .settings:
                    SettingsView()
                case .loading:
                    LoadingView()
                case .animalCollection:
                    AnimalCollectionView()
                case .battlePass:
                    BattlePassPlaceholderView()
                }
            } else {
                // App initialization screen
                LoadingView()
                    .onAppear {
                        initializeApp()
                    }
            }
        }
        .animation(.easeInOut(duration: 0.3), value: currentGameState)
        .animation(.easeInOut(duration: 0.5), value: isAppReady)
    }

    // MARK: - Initialization
    private func initializeApp() {
        Task {
            // Simulate app initialization
            try? await Task.sleep(nanoseconds: 1_000_000_000) // 1 second

            await MainActor.run {
                isAppReady = true
                print("🎮 Moo Farm initialized successfully")
                print("📱 Feature-based architecture ready")
            }
        }
    }
}

// MARK: - Placeholder Views
struct RhythmGamePlaceholderView: View {
    @EnvironmentObject private var dataService: DataService
    @State private var availableSongs: [Song] = []
    @State private var selectedSong: Song?
    @State private var showRhythmGame = false

    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                Text("🎵")
                    .font(.system(size: 80))

                Text("Rhythm Game")
                    .font(.largeTitle.bold())

                Text("Select a song to play!")
                    .font(.title2)
                    .foregroundColor(.secondary)

                // Song List
                ScrollView {
                    LazyVStack(spacing: 12) {
                        ForEach(availableSongs, id: \.objectID) { song in
                            SongRowView(song: song) {
                                selectedSong = song
                                showRhythmGame = true
                            }
                        }
                    }
                    .padding(.horizontal)
                }

                if availableSongs.isEmpty {
                    Text("No songs available. Complete the tutorial to unlock songs!")
                        .multilineTextAlignment(.center)
                        .padding(.horizontal, 40)
                        .foregroundColor(.secondary)
                }

                Spacer()
            }
            .padding()
            .navigationBarHidden(true)
            .onAppear {
                loadAvailableSongs()
            }
            .fullScreenCover(isPresented: $showRhythmGame) {
                if let song = selectedSong {
                    RhythmGameView(song: song)
                }
            }
        }
    }

    private func loadAvailableSongs() {
        let request = Song.fetchRequest()
        request.predicate = NSPredicate(format: "isUnlocked == YES")
        request.sortDescriptors = [
            NSSortDescriptor(key: "difficulty", ascending: true),
            NSSortDescriptor(key: "title", ascending: true)
        ]

        dataService.fetch(request)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { completion in
                    if case .failure(let error) = completion {
                        print("Failed to load songs: \(error)")
                    }
                },
                receiveValue: { songs in
                    self.availableSongs = songs
                }
            )
            .store(in: &Set<AnyCancellable>())
    }
}

struct SongRowView: View {
    let song: Song
    let onTap: () -> Void

    var body: some View {
        Button(action: onTap) {
            HStack(spacing: 15) {
                // Difficulty indicator
                Circle()
                    .fill(difficultyColor)
                    .frame(width: 12, height: 12)

                VStack(alignment: .leading, spacing: 4) {
                    Text(song.title ?? "Unknown Song")
                        .font(.headline)
                        .foregroundColor(.primary)

                    HStack {
                        Text(song.difficulty?.capitalized ?? "Normal")
                            .font(.caption)
                            .foregroundColor(.secondary)

                        Spacer()

                        Text("\(Int(song.bpm)) BPM")
                            .font(.caption)
                            .foregroundColor(.secondary)

                        Text("•")
                            .font(.caption)
                            .foregroundColor(.secondary)

                        Text("\(Int(song.duration))s")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }

                Spacer()

                Image(systemName: "play.circle.fill")
                    .font(.title2)
                    .foregroundColor(.blue)
            }
            .padding()
            .background(Color.gray.opacity(0.1))
            .cornerRadius(12)
        }
        .buttonStyle(PlainButtonStyle())
    }

    private var difficultyColor: Color {
        switch song.difficulty?.lowercased() {
        case "easy":
            return .green
        case "normal":
            return .yellow
        case "hard":
            return .red
        default:
            return .gray
        }
    }
}

struct FarmPlaceholderView: View {
    var body: some View {
        FarmView()
    }
}

struct BattlePassPlaceholderView: View {
    var body: some View {
        BattlePassView()
    }
}

struct LoadingView: View {
    @State private var isAnimating = false

    var body: some View {
        VStack(spacing: 30) {
            // Animated cow emoji
            Text("🐄")
                .font(.system(size: 80))
                .scaleEffect(isAnimating ? 1.2 : 1.0)
                .animation(.easeInOut(duration: 1.0).repeatForever(autoreverses: true), value: isAnimating)

            Text("Moo Farm")
                .font(.system(size: 36, weight: .bold, design: .rounded))

            Text("Loading...")
                .font(.title2)
                .foregroundColor(.secondary)

            ProgressView()
                .scaleEffect(1.5)
                .tint(.blue)
        }
        .onAppear {
            isAnimating = true
        }
    }
}

// MARK: - Preview
#Preview {
    ContentView()
        .environment(\.managedObjectContext, CoreDataStack.preview.viewContext)
        .environmentObject(AudioService.shared)
        .environmentObject(GameKitService.shared)
        .environmentObject(DataService.shared)
}
