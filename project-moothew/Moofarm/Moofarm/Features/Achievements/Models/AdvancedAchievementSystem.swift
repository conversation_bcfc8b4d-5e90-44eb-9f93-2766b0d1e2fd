//
//  AdvancedAchievementSystem.swift
//  Moofarm
//
//  Created by Project Moothew on 28/7/25.
//

import Foundation
import Combine

/// Advanced achievement system with cross-system chains and meta-achievements
/// Tracks complex player accomplishments across rhythm game and farm systems
class AdvancedAchievementSystem: ObservableObject {
    
    // MARK: - Published Properties
    @Published var achievements: [Achievement] = []
    @Published var unlockedAchievements: Set<String> = []
    @Published var achievementChains: [AchievementChain] = []
    @Published var metaAchievements: [MetaAchievement] = []
    @Published var recentUnlocks: [AchievementUnlock] = []
    @Published var totalAchievementPoints: Int = 0
    
    // MARK: - Services
    private let dataService: DataService
    private let gameManager: GameManager
    private let gameKitService: GameKitService
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Initialization
    init(
        dataService: DataService = DataService.shared,
        gameManager: GameManager = GameManager.shared,
        gameKitService: GameKitService = GameKitService.shared
    ) {
        self.dataService = dataService
        self.gameManager = gameManager
        self.gameKitService = gameKitService
        
        setupAchievements()
        loadPlayerProgress()
    }
    
    // MARK: - Setup
    private func setupAchievements() {
        achievements = generateAllAchievements()
        achievementChains = generateAchievementChains()
        metaAchievements = generateMetaAchievements()
        
        print("🏆 Achievement system initialized with \(achievements.count) achievements")
    }
    
    private func generateAllAchievements() -> [Achievement] {
        var allAchievements: [Achievement] = []
        
        // Rhythm Game Achievements
        allAchievements.append(contentsOf: generateRhythmAchievements())
        
        // Farm Achievements
        allAchievements.append(contentsOf: generateFarmAchievements())
        
        // Cross-System Achievements
        allAchievements.append(contentsOf: generateCrossSystemAchievements())
        
        // Social Achievements
        allAchievements.append(contentsOf: generateSocialAchievements())
        
        // Collection Achievements
        allAchievements.append(contentsOf: generateCollectionAchievements())
        
        // Progression Achievements
        allAchievements.append(contentsOf: generateProgressionAchievements())
        
        return allAchievements
    }
    
    private func generateRhythmAchievements() -> [Achievement] {
        return [
            Achievement(
                id: "first_song",
                name: "First Steps",
                description: "Complete your first rhythm song",
                category: .rhythm,
                requirement: .songCount(1),
                reward: AchievementReward(coins: 100, experience: 50, specialItem: nil),
                points: 10,
                rarity: .common,
                isSecret: false
            ),
            Achievement(
                id: "rhythm_novice",
                name: "Rhythm Novice",
                description: "Complete 10 rhythm songs",
                category: .rhythm,
                requirement: .songCount(10),
                reward: AchievementReward(coins: 500, experience: 200, specialItem: "Novice Badge"),
                points: 25,
                rarity: .common,
                isSecret: false
            ),
            Achievement(
                id: "perfect_master",
                name: "Perfect Master",
                description: "Get 1000 perfect hits",
                category: .rhythm,
                requirement: .perfectHits(1000),
                reward: AchievementReward(coins: 2000, experience: 500, specialItem: "Perfect Crown"),
                points: 50,
                rarity: .rare,
                isSecret: false
            ),
            Achievement(
                id: "combo_king",
                name: "Combo King",
                description: "Achieve a 200+ combo",
                category: .rhythm,
                requirement: .maxCombo(200),
                reward: AchievementReward(coins: 1500, experience: 300, specialItem: "Combo Crown"),
                points: 40,
                rarity: .rare,
                isSecret: false
            ),
            Achievement(
                id: "score_legend",
                name: "Score Legend",
                description: "Reach 1,000,000 total score",
                category: .rhythm,
                requirement: .totalScore(1000000),
                reward: AchievementReward(coins: 5000, experience: 1000, specialItem: "Legend Trophy"),
                points: 100,
                rarity: .legendary,
                isSecret: false
            )
        ]
    }
    
    private func generateFarmAchievements() -> [Achievement] {
        return [
            Achievement(
                id: "first_building",
                name: "Master Builder",
                description: "Place your first building",
                category: .farm,
                requirement: .buildingCount(1),
                reward: AchievementReward(coins: 200, experience: 100, specialItem: nil),
                points: 15,
                rarity: .common,
                isSecret: false
            ),
            Achievement(
                id: "farm_architect",
                name: "Farm Architect",
                description: "Build 20 structures",
                category: .farm,
                requirement: .buildingCount(20),
                reward: AchievementReward(coins: 2000, experience: 500, specialItem: "Architect Hat"),
                points: 60,
                rarity: .epic,
                isSecret: false
            ),
            Achievement(
                id: "happy_farmer",
                name: "Happy Farmer",
                description: "Maintain 90% farm happiness for 7 days",
                category: .farm,
                requirement: .farmHappiness(90, days: 7),
                reward: AchievementReward(coins: 1000, experience: 300, specialItem: "Happiness Aura"),
                points: 45,
                rarity: .rare,
                isSecret: false
            ),
            Achievement(
                id: "farm_tycoon",
                name: "Farm Tycoon",
                description: "Reach farm level 25",
                category: .farm,
                requirement: .farmLevel(25),
                reward: AchievementReward(coins: 10000, experience: 2000, specialItem: "Tycoon Crown"),
                points: 150,
                rarity: .legendary,
                isSecret: false
            )
        ]
    }
    
    private func generateCrossSystemAchievements() -> [Achievement] {
        return [
            Achievement(
                id: "harmony_master",
                name: "Harmony Master",
                description: "Reach farm level 10 and complete 50 songs",
                category: .crossSystem,
                requirement: .crossSystemGoal(farmLevel: 10, songsCompleted: 50),
                reward: AchievementReward(coins: 3000, experience: 750, specialItem: "Harmony Medal"),
                points: 75,
                rarity: .epic,
                isSecret: false
            ),
            Achievement(
                id: "perfect_synergy",
                name: "Perfect Synergy",
                description: "Get a perfect score while farm happiness is at 100%",
                category: .crossSystem,
                requirement: .perfectSynergy,
                reward: AchievementReward(coins: 2500, experience: 600, specialItem: "Synergy Crystal"),
                points: 80,
                rarity: .epic,
                isSecret: true
            ),
            Achievement(
                id: "ultimate_farmer_musician",
                name: "Ultimate Farmer-Musician",
                description: "Master both systems: Farm level 50 and 1M rhythm score",
                category: .crossSystem,
                requirement: .ultimateGoal(farmLevel: 50, totalScore: 1000000),
                reward: AchievementReward(coins: 25000, experience: 5000, specialItem: "Ultimate Crown"),
                points: 500,
                rarity: .legendary,
                isSecret: false
            )
        ]
    }
    
    private func generateSocialAchievements() -> [Achievement] {
        return [
            Achievement(
                id: "social_butterfly",
                name: "Social Butterfly",
                description: "Visit 10 friends' farms",
                category: .social,
                requirement: .farmVisits(10),
                reward: AchievementReward(coins: 800, experience: 200, specialItem: "Butterfly Wings"),
                points: 30,
                rarity: .common,
                isSecret: false
            ),
            Achievement(
                id: "helpful_neighbor",
                name: "Helpful Neighbor",
                description: "Help friends 50 times",
                category: .social,
                requirement: .friendHelp(50),
                reward: AchievementReward(coins: 1500, experience: 400, specialItem: "Helper Badge"),
                points: 40,
                rarity: .rare,
                isSecret: false
            )
        ]
    }
    
    private func generateCollectionAchievements() -> [Achievement] {
        return [
            Achievement(
                id: "animal_lover",
                name: "Animal Lover",
                description: "Unlock 10 different animals",
                category: .collection,
                requirement: .animalCount(10),
                reward: AchievementReward(coins: 1000, experience: 300, specialItem: "Animal Whisperer Title"),
                points: 35,
                rarity: .rare,
                isSecret: false
            ),
            Achievement(
                id: "legendary_collector",
                name: "Legendary Collector",
                description: "Unlock 5 legendary animals",
                category: .collection,
                requirement: .legendaryAnimals(5),
                reward: AchievementReward(coins: 5000, experience: 1200, specialItem: "Collector's Crown"),
                points: 120,
                rarity: .legendary,
                isSecret: false
            )
        ]
    }
    
    private func generateProgressionAchievements() -> [Achievement] {
        return [
            Achievement(
                id: "dedicated_player",
                name: "Dedicated Player",
                description: "Play for 30 consecutive days",
                category: .progression,
                requirement: .consecutiveDays(30),
                reward: AchievementReward(coins: 3000, experience: 800, specialItem: "Dedication Medal"),
                points: 70,
                rarity: .epic,
                isSecret: false
            ),
            Achievement(
                id: "time_master",
                name: "Time Master",
                description: "Play for 100 total hours",
                category: .progression,
                requirement: .playTime(360000), // 100 hours in seconds
                reward: AchievementReward(coins: 5000, experience: 1500, specialItem: "Time Crystal"),
                points: 100,
                rarity: .legendary,
                isSecret: false
            )
        ]
    }
    
    // MARK: - Achievement Chains
    private func generateAchievementChains() -> [AchievementChain] {
        return [
            AchievementChain(
                id: "rhythm_mastery_chain",
                name: "Rhythm Mastery",
                description: "Master the art of rhythm",
                achievementIds: ["first_song", "rhythm_novice", "perfect_master", "combo_king", "score_legend"],
                chainReward: AchievementReward(coins: 10000, experience: 2500, specialItem: "Rhythm Master Title"),
                chainPoints: 200
            ),
            AchievementChain(
                id: "farm_development_chain",
                name: "Farm Development",
                description: "Build the ultimate farm",
                achievementIds: ["first_building", "farm_architect", "happy_farmer", "farm_tycoon"],
                chainReward: AchievementReward(coins: 15000, experience: 3000, specialItem: "Farm Master Title"),
                chainPoints: 250
            ),
            AchievementChain(
                id: "cross_system_mastery",
                name: "Cross-System Mastery",
                description: "Master both rhythm and farm systems",
                achievementIds: ["harmony_master", "perfect_synergy", "ultimate_farmer_musician"],
                chainReward: AchievementReward(coins: 50000, experience: 10000, specialItem: "Grand Master Crown"),
                chainPoints: 1000
            )
        ]
    }
    
    // MARK: - Meta Achievements
    private func generateMetaAchievements() -> [MetaAchievement] {
        return [
            MetaAchievement(
                id: "achievement_hunter",
                name: "Achievement Hunter",
                description: "Unlock 50 achievements",
                requirement: .achievementCount(50),
                reward: AchievementReward(coins: 5000, experience: 1000, specialItem: "Hunter Badge"),
                points: 100
            ),
            MetaAchievement(
                id: "chain_master",
                name: "Chain Master",
                description: "Complete 3 achievement chains",
                requirement: .chainCount(3),
                reward: AchievementReward(coins: 10000, experience: 2000, specialItem: "Chain Master Title"),
                points: 200
            ),
            MetaAchievement(
                id: "point_collector",
                name: "Point Collector",
                description: "Earn 1000 achievement points",
                requirement: .totalPoints(1000),
                reward: AchievementReward(coins: 15000, experience: 3000, specialItem: "Point Crown"),
                points: 300
            )
        ]
    }
    
    // MARK: - Achievement Checking
    func checkAchievements(for player: Player) -> [String] {
        var newlyUnlocked: [String] = []
        
        for achievement in achievements {
            if !unlockedAchievements.contains(achievement.id) {
                if checkAchievementRequirement(achievement.requirement, for: player) {
                    unlockAchievement(achievement, for: player)
                    newlyUnlocked.append(achievement.id)
                }
            }
        }
        
        // Check achievement chains
        checkAchievementChains()
        
        // Check meta achievements
        checkMetaAchievements()
        
        return newlyUnlocked
    }
    
    private func checkAchievementRequirement(_ requirement: AchievementRequirement, for player: Player) -> Bool {
        switch requirement {
        case .songCount(let count):
            let completedSongs = player.songProgress?.allObjects.compactMap { $0 as? SongProgress }.filter { $0.isCompleted }.count ?? 0
            return completedSongs >= count
            
        case .perfectHits(let count):
            let totalPerfectHits = player.songProgress?.allObjects.compactMap { $0 as? SongProgress }.map { Int($0.perfectHits) }.reduce(0, +) ?? 0
            return totalPerfectHits >= count
            
        case .maxCombo(let combo):
            let bestCombo = player.songProgress?.allObjects.compactMap { $0 as? SongProgress }.map { Int($0.bestCombo) }.max() ?? 0
            return bestCombo >= combo
            
        case .totalScore(let score):
            let totalScore = player.songProgress?.allObjects.compactMap { $0 as? SongProgress }.map { Int($0.highScore) }.reduce(0, +) ?? 0
            return totalScore >= score
            
        case .buildingCount(let count):
            return Int(player.farm?.totalBuildings ?? 0) >= count
            
        case .farmLevel(let level):
            return Int(player.farm?.level ?? 0) >= level
            
        case .farmHappiness(let happiness, let days):
            // This would require tracking happiness over time
            return Float(player.farm?.happiness ?? 0) * 100 >= Float(happiness)
            
        case .animalCount(let count):
            let unlockedAnimals = player.animals?.allObjects.compactMap { $0 as? Animal }.filter { $0.isUnlocked }.count ?? 0
            return unlockedAnimals >= count
            
        case .legendaryAnimals(let count):
            let legendaryAnimals = player.animals?.allObjects.compactMap { $0 as? Animal }.filter { 
                $0.isUnlocked && $0.animalRarity == .legendary 
            }.count ?? 0
            return legendaryAnimals >= count
            
        case .consecutiveDays(let days):
            return Int(player.consecutiveDays) >= days
            
        case .playTime(let seconds):
            return Int(player.totalPlayTime) >= seconds
            
        case .farmVisits(let count):
            // Would track social visits
            return false // Placeholder
            
        case .friendHelp(let count):
            // Would track friend help actions
            return false // Placeholder
            
        case .crossSystemGoal(let farmLevel, let songsCompleted):
            let currentFarmLevel = Int(player.farm?.level ?? 0)
            let completedSongs = player.songProgress?.allObjects.compactMap { $0 as? SongProgress }.filter { $0.isCompleted }.count ?? 0
            return currentFarmLevel >= farmLevel && completedSongs >= songsCompleted
            
        case .perfectSynergy:
            let farmHappiness = player.farm?.happiness ?? 0
            // Would need to check if last rhythm game was perfect while farm happiness was 100%
            return farmHappiness >= 1.0 // Placeholder
            
        case .ultimateGoal(let farmLevel, let totalScore):
            let currentFarmLevel = Int(player.farm?.level ?? 0)
            let playerTotalScore = player.songProgress?.allObjects.compactMap { $0 as? SongProgress }.map { Int($0.highScore) }.reduce(0, +) ?? 0
            return currentFarmLevel >= farmLevel && playerTotalScore >= totalScore
        }
    }
    
    private func unlockAchievement(_ achievement: Achievement, for player: Player) {
        unlockedAchievements.insert(achievement.id)
        totalAchievementPoints += achievement.points
        
        // Apply rewards
        player.add(
            coins: Int32(achievement.reward.coins),
            experience: Int32(achievement.reward.experience)
        )
        
        // Create unlock record
        let unlock = AchievementUnlock(
            achievementId: achievement.id,
            achievementName: achievement.name,
            unlockedAt: Date(),
            pointsEarned: achievement.points
        )
        recentUnlocks.append(unlock)
        
        // Keep only recent unlocks (last 10)
        if recentUnlocks.count > 10 {
            recentUnlocks.removeFirst()
        }
        
        // Report to GameKit
        if gameKitService.isAuthenticated {
            _ = gameKitService.reportAchievement(achievementId: achievement.id)
        }
        
        print("🏆 Achievement unlocked: \(achievement.name) (+\(achievement.points) points)")
    }
    
    private func checkAchievementChains() {
        for chain in achievementChains {
            if !unlockedAchievements.contains("chain_\(chain.id)") {
                let chainCompleted = chain.achievementIds.allSatisfy { unlockedAchievements.contains($0) }
                
                if chainCompleted {
                    unlockAchievementChain(chain)
                }
            }
        }
    }
    
    private func unlockAchievementChain(_ chain: AchievementChain) {
        unlockedAchievements.insert("chain_\(chain.id)")
        totalAchievementPoints += chain.chainPoints
        
        // Apply chain rewards
        if let player = gameManager.currentPlayer {
            player.add(
                coins: Int32(chain.chainReward.coins),
                experience: Int32(chain.chainReward.experience)
            )
        }
        
        print("🔗 Achievement chain completed: \(chain.name) (+\(chain.chainPoints) points)")
    }
    
    private func checkMetaAchievements() {
        for metaAchievement in metaAchievements {
            if !unlockedAchievements.contains("meta_\(metaAchievement.id)") {
                let requirementMet = checkMetaAchievementRequirement(metaAchievement.requirement)
                
                if requirementMet {
                    unlockMetaAchievement(metaAchievement)
                }
            }
        }
    }
    
    private func checkMetaAchievementRequirement(_ requirement: MetaAchievementRequirement) -> Bool {
        switch requirement {
        case .achievementCount(let count):
            return unlockedAchievements.count >= count
        case .chainCount(let count):
            let completedChains = achievementChains.filter { chain in
                unlockedAchievements.contains("chain_\(chain.id)")
            }.count
            return completedChains >= count
        case .totalPoints(let points):
            return totalAchievementPoints >= points
        }
    }
    
    private func unlockMetaAchievement(_ metaAchievement: MetaAchievement) {
        unlockedAchievements.insert("meta_\(metaAchievement.id)")
        totalAchievementPoints += metaAchievement.points
        
        // Apply rewards
        if let player = gameManager.currentPlayer {
            player.add(
                coins: Int32(metaAchievement.reward.coins),
                experience: Int32(metaAchievement.reward.experience)
            )
        }
        
        print("🌟 Meta achievement unlocked: \(metaAchievement.name) (+\(metaAchievement.points) points)")
    }
    
    // MARK: - Data Persistence
    private func loadPlayerProgress() {
        // In production, load from Core Data
        // For now, use empty state
        unlockedAchievements = []
        totalAchievementPoints = 0
        recentUnlocks = []
    }
    
    // MARK: - Public Interface
    func getAchievementProgress(_ achievement: Achievement) -> Float {
        guard let player = gameManager.currentPlayer else { return 0.0 }
        
        // Calculate progress percentage based on requirement
        switch achievement.requirement {
        case .songCount(let target):
            let current = player.songProgress?.allObjects.compactMap { $0 as? SongProgress }.filter { $0.isCompleted }.count ?? 0
            return Float(current) / Float(target)
            
        case .perfectHits(let target):
            let current = player.songProgress?.allObjects.compactMap { $0 as? SongProgress }.map { Int($0.perfectHits) }.reduce(0, +) ?? 0
            return Float(current) / Float(target)
            
        case .buildingCount(let target):
            let current = Int(player.farm?.totalBuildings ?? 0)
            return Float(current) / Float(target)
            
        case .animalCount(let target):
            let current = player.animals?.allObjects.compactMap { $0 as? Animal }.filter { $0.isUnlocked }.count ?? 0
            return Float(current) / Float(target)
            
        default:
            return 0.0
        }
    }
    
    func getCompletedChains() -> [AchievementChain] {
        return achievementChains.filter { chain in
            unlockedAchievements.contains("chain_\(chain.id)")
        }
    }
    
    func getAchievementsByCategory(_ category: Achievement.AchievementCategory) -> [Achievement] {
        return achievements.filter { $0.category == category }
    }
}
