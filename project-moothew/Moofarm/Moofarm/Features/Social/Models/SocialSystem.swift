//
//  SocialSystem.swift
//  Moofarm
//
//  Created by Project Moothew on 28/7/25.
//

import Foundation
import Combine
import GameKit

/// Social features system for farm visiting and friend interactions
/// Integrates with GameKit for friend discovery and leaderboards
class SocialSystem: ObservableObject {
    
    // MARK: - Published Properties
    @Published var friends: [SocialFriend] = []
    @Published var farmVisits: [FarmVisit] = []
    @Published var leaderboards: [LeaderboardEntry] = []
    @Published var socialChallenges: [SocialChallenge] = []
    @Published var isLoadingFriends = false
    @Published var isLoadingLeaderboards = false
    
    // MARK: - Services
    private let gameKitService: GameKitService
    private let dataService: DataService
    private let gameManager: GameManager
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Initialization
    init(
        gameKitService: GameKitService = GameKitService.shared,
        dataService: DataService = DataService.shared,
        gameManager: GameManager = GameManager.shared
    ) {
        self.gameKitService = gameKitService
        self.dataService = dataService
        self.gameManager = gameManager
        
        setupBindings()
        loadSocialData()
    }
    
    // MARK: - Setup
    private func setupBindings() {
        // Listen for GameKit authentication
        gameKitService.$isAuthenticated
            .sink { [weak self] isAuthenticated in
                if isAuthenticated {
                    self?.loadFriends()
                    self?.loadLeaderboards()
                }
            }
            .store(in: &cancellables)
    }
    
    // MARK: - Friend Management
    func loadFriends() {
        guard gameKitService.isAuthenticated else { return }
        
        isLoadingFriends = true
        
        // Load GameKit friends
        gameKitService.loadFriends()
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completion in
                    self?.isLoadingFriends = false
                    if case .failure(let error) = completion {
                        print("❌ Failed to load friends: \(error)")
                    }
                },
                receiveValue: { [weak self] gkFriends in
                    self?.processFriends(gkFriends)
                }
            )
            .store(in: &cancellables)
    }
    
    private func processFriends(_ gkFriends: [GKPlayer]) {
        friends = gkFriends.map { gkPlayer in
            SocialFriend(
                id: gkPlayer.gamePlayerID,
                displayName: gkPlayer.displayName,
                alias: gkPlayer.alias,
                farmLevel: generateRandomFarmLevel(), // In production, fetch from server
                lastActive: generateRandomLastActive(),
                farmRating: generateRandomFarmRating(),
                favoriteAnimal: AnimalType.allCases.randomElement() ?? .cow,
                isOnline: Bool.random()
            )
        }
        
        print("✅ Loaded \(friends.count) friends")
    }
    
    // MARK: - Farm Visiting
    func visitFriend(_ friend: SocialFriend) -> AnyPublisher<FarmVisitResult, Error> {
        return Future { [weak self] promise in
            // Simulate farm visit
            let visit = FarmVisit(
                id: UUID().uuidString,
                friendId: friend.id,
                friendName: friend.displayName,
                visitedAt: Date(),
                farmSnapshot: self?.generateFarmSnapshot(for: friend) ?? FarmSnapshot.empty,
                giftsGiven: [],
                helpProvided: []
            )
            
            self?.farmVisits.append(visit)
            
            // Generate visit rewards
            let rewards = FarmVisitRewards(
                coins: Int.random(in: 50...200),
                experience: Int.random(in: 10...50),
                socialPoints: Int.random(in: 5...20),
                specialItems: []
            )
            
            // Apply rewards to player
            self?.gameManager.currentPlayer?.add(
                coins: Int32(rewards.coins),
                experience: Int32(rewards.experience)
            )
            
            let result = FarmVisitResult(
                visit: visit,
                rewards: rewards,
                canHelpAgain: false // Can only help once per day
            )
            
            promise(.success(result))
            print("🏡 Visited \(friend.displayName)'s farm")
        }
        .eraseToAnyPublisher()
    }
    
    func helpFriend(_ friend: SocialFriend, helpType: FarmHelpType) -> AnyPublisher<FarmHelpResult, Error> {
        return Future { [weak self] promise in
            let help = FarmHelp(
                id: UUID().uuidString,
                friendId: friend.id,
                helpType: helpType,
                providedAt: Date(),
                rewards: FarmHelpRewards(
                    coins: helpType.coinReward,
                    experience: helpType.xpReward,
                    socialPoints: helpType.socialPointReward
                )
            )
            
            // Apply rewards
            self?.gameManager.currentPlayer?.add(
                coins: Int32(help.rewards.coins),
                experience: Int32(help.rewards.experience)
            )
            
            let result = FarmHelpResult(
                help: help,
                friendshipIncrease: 10,
                canHelpAgain: false
            )
            
            promise(.success(result))
            print("🤝 Helped \(friend.displayName) with \(helpType.displayName)")
        }
        .eraseToAnyPublisher()
    }
    
    // MARK: - Leaderboards
    func loadLeaderboards() {
        guard gameKitService.isAuthenticated else { return }
        
        isLoadingLeaderboards = true
        
        // Load multiple leaderboards
        let leaderboardIds = ["rhythm_score", "farm_rating", "total_animals"]
        
        Publishers.MergeMany(
            leaderboardIds.map { leaderboardId in
                gameKitService.loadLeaderboard(leaderboardId: leaderboardId)
                    .map { scores in
                        scores.map { score in
                            LeaderboardEntry(
                                playerId: score.player.gamePlayerID,
                                playerName: score.player.displayName,
                                score: Int(score.value),
                                rank: score.rank,
                                category: LeaderboardCategory(rawValue: leaderboardId) ?? .rhythmScore
                            )
                        }
                    }
            }
        )
        .collect()
        .receive(on: DispatchQueue.main)
        .sink(
            receiveCompletion: { [weak self] completion in
                self?.isLoadingLeaderboards = false
                if case .failure(let error) = completion {
                    print("❌ Failed to load leaderboards: \(error)")
                }
            },
            receiveValue: { [weak self] leaderboardArrays in
                self?.leaderboards = leaderboardArrays.flatMap { $0 }
                print("✅ Loaded leaderboards with \(self?.leaderboards.count ?? 0) entries")
            }
        )
        .store(in: &cancellables)
    }
    
    // MARK: - Social Challenges
    func generateSocialChallenges() {
        socialChallenges = [
            SocialChallenge(
                id: "visit_friends",
                title: "Social Butterfly",
                description: "Visit 5 friends' farms",
                type: .farmVisits,
                targetValue: 5,
                currentProgress: 0,
                rewards: SocialChallengeRewards(
                    coins: 500,
                    experience: 100,
                    socialPoints: 50,
                    specialReward: "Friendship Trophy"
                ),
                expiresAt: Calendar.current.date(byAdding: .weekOfYear, value: 1, to: Date()) ?? Date()
            ),
            SocialChallenge(
                id: "help_friends",
                title: "Helpful Neighbor",
                description: "Help friends 10 times",
                type: .friendHelp,
                targetValue: 10,
                currentProgress: 0,
                rewards: SocialChallengeRewards(
                    coins: 300,
                    experience: 75,
                    socialPoints: 100,
                    specialReward: "Helper Badge"
                ),
                expiresAt: Calendar.current.date(byAdding: .weekOfYear, value: 1, to: Date()) ?? Date()
            ),
            SocialChallenge(
                id: "leaderboard_climb",
                title: "Competitive Spirit",
                description: "Reach top 10 in any leaderboard",
                type: .leaderboardRank,
                targetValue: 10,
                currentProgress: getCurrentBestRank(),
                rewards: SocialChallengeRewards(
                    coins: 1000,
                    experience: 200,
                    socialPoints: 150,
                    specialReward: "Champion Crown"
                ),
                expiresAt: Calendar.current.date(byAdding: .month, value: 1, to: Date()) ?? Date()
            )
        ]
    }
    
    // MARK: - Gift System
    func sendGift(to friend: SocialFriend, gift: SocialGift) -> AnyPublisher<Bool, Error> {
        return Future { promise in
            // Simulate sending gift
            print("🎁 Sent \(gift.displayName) to \(friend.displayName)")
            promise(.success(true))
        }
        .eraseToAnyPublisher()
    }
    
    func receiveGifts() -> AnyPublisher<[ReceivedGift], Error> {
        return Future { promise in
            // Simulate receiving gifts
            let gifts = [
                ReceivedGift(
                    id: UUID().uuidString,
                    fromFriendId: "friend1",
                    fromFriendName: "Alice",
                    gift: SocialGift.coins(amount: 100),
                    receivedAt: Date(),
                    message: "Thanks for helping with my farm!"
                ),
                ReceivedGift(
                    id: UUID().uuidString,
                    fromFriendId: "friend2",
                    fromFriendName: "Bob",
                    gift: SocialGift.animal(type: .pig),
                    receivedAt: Date(),
                    message: "Hope you like this little guy!"
                )
            ]
            
            promise(.success(gifts))
        }
        .eraseToAnyPublisher()
    }
    
    // MARK: - Helper Methods
    private func loadSocialData() {
        generateSocialChallenges()
        
        if gameKitService.isAuthenticated {
            loadFriends()
            loadLeaderboards()
        }
    }
    
    private func generateFarmSnapshot(for friend: SocialFriend) -> FarmSnapshot {
        return FarmSnapshot(
            farmName: "\(friend.displayName)'s Farm",
            level: friend.farmLevel,
            happiness: Float.random(in: 0.5...1.0),
            buildings: generateRandomBuildings(),
            animals: generateRandomAnimals(),
            decorations: generateRandomDecorations(),
            specialFeatures: []
        )
    }
    
    private func generateRandomBuildings() -> [FarmSnapshot.BuildingInfo] {
        let buildingTypes = BuildingConfiguration.BuildingType.allCases
        return (0..<Int.random(in: 3...8)).map { _ in
            FarmSnapshot.BuildingInfo(
                type: buildingTypes.randomElement() ?? .barn,
                level: Int.random(in: 1...3),
                position: GridPosition(x: Int.random(in: 0...11), y: Int.random(in: 0...11))
            )
        }
    }
    
    private func generateRandomAnimals() -> [FarmSnapshot.AnimalInfo] {
        let animalTypes = AnimalType.allCases
        return (0..<Int.random(in: 2...6)).map { _ in
            FarmSnapshot.AnimalInfo(
                type: animalTypes.randomElement() ?? .cow,
                level: Int.random(in: 1...10),
                position: GridPosition(x: Int.random(in: 0...11), y: Int.random(in: 0...11))
            )
        }
    }
    
    private func generateRandomDecorations() -> [FarmSnapshot.DecorationInfo] {
        return (0..<Int.random(in: 1...5)).map { _ in
            FarmSnapshot.DecorationInfo(
                type: "flower_\(Int.random(in: 1...5))",
                position: GridPosition(x: Int.random(in: 0...11), y: Int.random(in: 0...11))
            )
        }
    }
    
    private func generateRandomFarmLevel() -> Int {
        return Int.random(in: 1...20)
    }
    
    private func generateRandomLastActive() -> Date {
        let daysAgo = Int.random(in: 0...7)
        return Calendar.current.date(byAdding: .day, value: -daysAgo, to: Date()) ?? Date()
    }
    
    private func generateRandomFarmRating() -> Int {
        return Int.random(in: 100...2000)
    }
    
    private func getCurrentBestRank() -> Int {
        return leaderboards.compactMap { entry in
            entry.playerId == gameKitService.localPlayer?.gamePlayerID ? entry.rank : nil
        }.min() ?? 999
    }
}

// MARK: - Social Models
struct SocialFriend: Identifiable {
    let id: String
    let displayName: String
    let alias: String
    let farmLevel: Int
    let lastActive: Date
    let farmRating: Int
    let favoriteAnimal: AnimalType
    let isOnline: Bool
    
    var lastActiveString: String {
        let formatter = RelativeDateTimeFormatter()
        formatter.unitsStyle = .abbreviated
        return formatter.localizedString(for: lastActive, relativeTo: Date())
    }
}

struct FarmVisit: Identifiable {
    let id: String
    let friendId: String
    let friendName: String
    let visitedAt: Date
    let farmSnapshot: FarmSnapshot
    let giftsGiven: [SocialGift]
    let helpProvided: [FarmHelpType]
}

struct FarmVisitResult {
    let visit: FarmVisit
    let rewards: FarmVisitRewards
    let canHelpAgain: Bool
}

struct FarmVisitRewards {
    let coins: Int
    let experience: Int
    let socialPoints: Int
    let specialItems: [String]
}

struct FarmSnapshot {
    let farmName: String
    let level: Int
    let happiness: Float
    let buildings: [BuildingInfo]
    let animals: [AnimalInfo]
    let decorations: [DecorationInfo]
    let specialFeatures: [String]
    
    struct BuildingInfo {
        let type: BuildingConfiguration.BuildingType
        let level: Int
        let position: GridPosition
    }
    
    struct AnimalInfo {
        let type: AnimalType
        let level: Int
        let position: GridPosition
    }
    
    struct DecorationInfo {
        let type: String
        let position: GridPosition
    }
    
    static let empty = FarmSnapshot(
        farmName: "Empty Farm",
        level: 1,
        happiness: 0.5,
        buildings: [],
        animals: [],
        decorations: [],
        specialFeatures: []
    )
}

enum FarmHelpType: String, CaseIterable {
    case waterPlants = "water_plants"
    case feedAnimals = "feed_animals"
    case collectResources = "collect_resources"
    case repairBuildings = "repair_buildings"
    case clearWeeds = "clear_weeds"
    
    var displayName: String {
        switch self {
        case .waterPlants: return "Water Plants"
        case .feedAnimals: return "Feed Animals"
        case .collectResources: return "Collect Resources"
        case .repairBuildings: return "Repair Buildings"
        case .clearWeeds: return "Clear Weeds"
        }
    }
    
    var icon: String {
        switch self {
        case .waterPlants: return "💧"
        case .feedAnimals: return "🌾"
        case .collectResources: return "📦"
        case .repairBuildings: return "🔨"
        case .clearWeeds: return "🌿"
        }
    }
    
    var coinReward: Int {
        switch self {
        case .waterPlants: return 50
        case .feedAnimals: return 75
        case .collectResources: return 100
        case .repairBuildings: return 125
        case .clearWeeds: return 60
        }
    }
    
    var xpReward: Int {
        switch self {
        case .waterPlants: return 10
        case .feedAnimals: return 15
        case .collectResources: return 20
        case .repairBuildings: return 25
        case .clearWeeds: return 12
        }
    }
    
    var socialPointReward: Int {
        switch self {
        case .waterPlants: return 5
        case .feedAnimals: return 8
        case .collectResources: return 10
        case .repairBuildings: return 12
        case .clearWeeds: return 6
        }
    }
}

struct FarmHelp {
    let id: String
    let friendId: String
    let helpType: FarmHelpType
    let providedAt: Date
    let rewards: FarmHelpRewards
}

struct FarmHelpResult {
    let help: FarmHelp
    let friendshipIncrease: Int
    let canHelpAgain: Bool
}

struct FarmHelpRewards {
    let coins: Int
    let experience: Int
    let socialPoints: Int
}

enum SocialGift {
    case coins(amount: Int)
    case gems(amount: Int)
    case animal(type: AnimalType)
    case decoration(type: String)
    case resource(type: String, amount: Int)
    
    var displayName: String {
        switch self {
        case .coins(let amount): return "\(amount) Coins"
        case .gems(let amount): return "\(amount) Gems"
        case .animal(let type): return type.displayName
        case .decoration(let type): return type.capitalized
        case .resource(let type, let amount): return "\(amount) \(type)"
        }
    }
    
    var icon: String {
        switch self {
        case .coins: return "🪙"
        case .gems: return "💎"
        case .animal(let type): return type.emoji
        case .decoration: return "🌸"
        case .resource: return "📦"
        }
    }
}

struct ReceivedGift: Identifiable {
    let id: String
    let fromFriendId: String
    let fromFriendName: String
    let gift: SocialGift
    let receivedAt: Date
    let message: String
}

struct LeaderboardEntry: Identifiable {
    let id = UUID()
    let playerId: String
    let playerName: String
    let score: Int
    let rank: Int
    let category: LeaderboardCategory
}

enum LeaderboardCategory: String, CaseIterable {
    case rhythmScore = "rhythm_score"
    case farmRating = "farm_rating"
    case totalAnimals = "total_animals"
    
    var displayName: String {
        switch self {
        case .rhythmScore: return "Rhythm Score"
        case .farmRating: return "Farm Rating"
        case .totalAnimals: return "Animal Collection"
        }
    }
    
    var icon: String {
        switch self {
        case .rhythmScore: return "🎵"
        case .farmRating: return "⭐"
        case .totalAnimals: return "🐾"
        }
    }
}

struct SocialChallenge: Identifiable {
    let id: String
    let title: String
    let description: String
    let type: SocialChallengeType
    let targetValue: Int
    var currentProgress: Int
    let rewards: SocialChallengeRewards
    let expiresAt: Date
    
    var isCompleted: Bool {
        return currentProgress >= targetValue
    }
    
    var progressPercentage: Float {
        return Float(currentProgress) / Float(targetValue)
    }
}

enum SocialChallengeType: String, CaseIterable {
    case farmVisits = "farm_visits"
    case friendHelp = "friend_help"
    case leaderboardRank = "leaderboard_rank"
    case giftsExchanged = "gifts_exchanged"
    
    var displayName: String {
        switch self {
        case .farmVisits: return "Farm Visits"
        case .friendHelp: return "Friend Help"
        case .leaderboardRank: return "Leaderboard Rank"
        case .giftsExchanged: return "Gifts Exchanged"
        }
    }
}

struct SocialChallengeRewards {
    let coins: Int
    let experience: Int
    let socialPoints: Int
    let specialReward: String
}
