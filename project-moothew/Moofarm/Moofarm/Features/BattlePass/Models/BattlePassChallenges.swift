//
//  BattlePassChallenges.swift
//  Moofarm
//
//  Created by Project Moothew on 28/7/25.
//

import Foundation

// MARK: - Challenge Protocol
protocol BattlePassChallenge {
    var id: String { get }
    var title: String { get }
    var description: String { get }
    var targetValue: Int { get }
    var currentProgress: Int { get set }
    var xpReward: Int { get }
    var expiresAt: Date { get }
    var isCompleted: Bool { get }
    var isRewardClaimed: Bool { get set }
    
    mutating func updateProgress(amount: Int)
    func canProgress(from source: XPSource) -> Bool
}

// MARK: - Daily Challenge
struct DailyChallenge: BattlePassChallenge, Identifiable {
    let id: String
    let title: String
    let description: String
    let type: DailyChallengeType
    let targetValue: Int
    var currentProgress: Int
    let xpReward: Int
    let expiresAt: Date
    var isRewardClaimed: Bool = false
    
    var isCompleted: Bool {
        return currentProgress >= targetValue
    }
    
    var progressPercentage: Float {
        return Float(currentProgress) / Float(targetValue)
    }
    
    mutating func updateProgress(amount: Int) {
        currentProgress = min(currentProgress + amount, targetValue)
    }
    
    func canProgress(from source: XPSource) -> Bool {
        switch type {
        case .rhythmSongs:
            return source == .rhythmGame
        case .buildingsPlaced:
            return source == .farmBuilding
        case .perfectCombo:
            return source == .rhythmGame
        case .animalsCared:
            return source == .animalCare
        case .farmHappiness:
            return source == .farmBuilding || source == .animalCare
        case .dailyLogin:
            return source == .dailyLogin
        case .coinsEarned:
            return source == .rhythmGame || source == .farmBuilding
        case .songsCompleted:
            return source == .rhythmGame
        }
    }
}

// MARK: - Weekly Challenge
struct WeeklyChallenge: BattlePassChallenge, Identifiable {
    let id: String
    let title: String
    let description: String
    let type: WeeklyChallengeType
    let targetValue: Int
    var currentProgress: Int
    let xpReward: Int
    let expiresAt: Date
    var isRewardClaimed: Bool = false
    
    var isCompleted: Bool {
        return currentProgress >= targetValue
    }
    
    var progressPercentage: Float {
        return Float(currentProgress) / Float(targetValue)
    }
    
    mutating func updateProgress(amount: Int) {
        currentProgress = min(currentProgress + amount, targetValue)
    }
    
    func canProgress(from source: XPSource) -> Bool {
        switch type {
        case .crossSystem:
            return source == .rhythmGame || source == .farmBuilding
        case .animalsUnlocked:
            return source == .rhythmGame || source == .achievement
        case .totalScore:
            return source == .rhythmGame
        case .farmLevel:
            return source == .farmBuilding
        case .perfectHits:
            return source == .rhythmGame
        case .buildingUpgrades:
            return source == .farmBuilding
        case .socialInteractions:
            return source == .social
        case .streakDays:
            return source == .dailyLogin
        }
    }
}

// MARK: - Challenge Types
enum DailyChallengeType: String, CaseIterable {
    case rhythmSongs = "rhythm_songs"
    case buildingsPlaced = "buildings_placed"
    case perfectCombo = "perfect_combo"
    case animalsCared = "animals_cared"
    case farmHappiness = "farm_happiness"
    case dailyLogin = "daily_login"
    case coinsEarned = "coins_earned"
    case songsCompleted = "songs_completed"
    
    var displayName: String {
        switch self {
        case .rhythmSongs: return "Rhythm Songs"
        case .buildingsPlaced: return "Buildings Placed"
        case .perfectCombo: return "Perfect Combo"
        case .animalsCared: return "Animals Cared"
        case .farmHappiness: return "Farm Happiness"
        case .dailyLogin: return "Daily Login"
        case .coinsEarned: return "Coins Earned"
        case .songsCompleted: return "Songs Completed"
        }
    }
    
    var icon: String {
        switch self {
        case .rhythmSongs: return "🎵"
        case .buildingsPlaced: return "🏗️"
        case .perfectCombo: return "⭐"
        case .animalsCared: return "🐾"
        case .farmHappiness: return "😊"
        case .dailyLogin: return "📅"
        case .coinsEarned: return "🪙"
        case .songsCompleted: return "🎼"
        }
    }
}

enum WeeklyChallengeType: String, CaseIterable {
    case crossSystem = "cross_system"
    case animalsUnlocked = "animals_unlocked"
    case totalScore = "total_score"
    case farmLevel = "farm_level"
    case perfectHits = "perfect_hits"
    case buildingUpgrades = "building_upgrades"
    case socialInteractions = "social_interactions"
    case streakDays = "streak_days"
    
    var displayName: String {
        switch self {
        case .crossSystem: return "Cross System"
        case .animalsUnlocked: return "Animals Unlocked"
        case .totalScore: return "Total Score"
        case .farmLevel: return "Farm Level"
        case .perfectHits: return "Perfect Hits"
        case .buildingUpgrades: return "Building Upgrades"
        case .socialInteractions: return "Social Interactions"
        case .streakDays: return "Streak Days"
        }
    }
    
    var icon: String {
        switch self {
        case .crossSystem: return "🔄"
        case .animalsUnlocked: return "🐾"
        case .totalScore: return "🎯"
        case .farmLevel: return "🏡"
        case .perfectHits: return "💯"
        case .buildingUpgrades: return "⬆️"
        case .socialInteractions: return "👥"
        case .streakDays: return "🔥"
        }
    }
}

// MARK: - Challenge Factory
class ChallengeFactory {
    
    static func generateDailyChallenges(for date: Date) -> [DailyChallenge] {
        let expiryDate = Calendar.current.date(byAdding: .day, value: 1, to: date) ?? date
        
        let allChallenges: [DailyChallenge] = [
            // Rhythm Game Challenges
            DailyChallenge(
                id: "daily_rhythm_songs",
                title: "Rhythm Master",
                description: "Complete 3 rhythm songs",
                type: .rhythmSongs,
                targetValue: 3,
                currentProgress: 0,
                xpReward: 200,
                expiresAt: expiryDate
            ),
            DailyChallenge(
                id: "daily_perfect_combo",
                title: "Perfect Combo",
                description: "Achieve a 50+ combo",
                type: .perfectCombo,
                targetValue: 50,
                currentProgress: 0,
                xpReward: 300,
                expiresAt: expiryDate
            ),
            DailyChallenge(
                id: "daily_songs_completed",
                title: "Song Completionist",
                description: "Complete 5 different songs",
                type: .songsCompleted,
                targetValue: 5,
                currentProgress: 0,
                xpReward: 250,
                expiresAt: expiryDate
            ),
            
            // Farm Challenges
            DailyChallenge(
                id: "daily_buildings_placed",
                title: "Builder",
                description: "Place 2 buildings",
                type: .buildingsPlaced,
                targetValue: 2,
                currentProgress: 0,
                xpReward: 150,
                expiresAt: expiryDate
            ),
            DailyChallenge(
                id: "daily_animals_cared",
                title: "Animal Caretaker",
                description: "Care for 5 animals",
                type: .animalsCared,
                targetValue: 5,
                currentProgress: 0,
                xpReward: 180,
                expiresAt: expiryDate
            ),
            DailyChallenge(
                id: "daily_farm_happiness",
                title: "Happy Farm",
                description: "Reach 80% farm happiness",
                type: .farmHappiness,
                targetValue: 80,
                currentProgress: 0,
                xpReward: 220,
                expiresAt: expiryDate
            ),
            
            // General Challenges
            DailyChallenge(
                id: "daily_coins_earned",
                title: "Coin Collector",
                description: "Earn 1000 coins",
                type: .coinsEarned,
                targetValue: 1000,
                currentProgress: 0,
                xpReward: 150,
                expiresAt: expiryDate
            ),
            DailyChallenge(
                id: "daily_login",
                title: "Daily Visitor",
                description: "Log in to the game",
                type: .dailyLogin,
                targetValue: 1,
                currentProgress: 0,
                xpReward: 100,
                expiresAt: expiryDate
            )
        ]
        
        // Return 3 random challenges
        return Array(allChallenges.shuffled().prefix(3))
    }
    
    static func generateWeeklyChallenges(for date: Date) -> [WeeklyChallenge] {
        let expiryDate = Calendar.current.date(byAdding: .weekOfYear, value: 1, to: date) ?? date
        
        let allChallenges: [WeeklyChallenge] = [
            // Cross-System Challenges
            WeeklyChallenge(
                id: "weekly_cross_system",
                title: "Farm & Rhythm Master",
                description: "Complete 20 songs and reach farm level 5",
                type: .crossSystem,
                targetValue: 1,
                currentProgress: 0,
                xpReward: 1000,
                expiresAt: expiryDate
            ),
            
            // Collection Challenges
            WeeklyChallenge(
                id: "weekly_animals_unlocked",
                title: "Animal Collector",
                description: "Unlock 5 new animals",
                type: .animalsUnlocked,
                targetValue: 5,
                currentProgress: 0,
                xpReward: 800,
                expiresAt: expiryDate
            ),
            
            // Performance Challenges
            WeeklyChallenge(
                id: "weekly_total_score",
                title: "High Scorer",
                description: "Achieve 500,000 total score",
                type: .totalScore,
                targetValue: 500000,
                currentProgress: 0,
                xpReward: 1200,
                expiresAt: expiryDate
            ),
            WeeklyChallenge(
                id: "weekly_perfect_hits",
                title: "Precision Master",
                description: "Get 1000 perfect hits",
                type: .perfectHits,
                targetValue: 1000,
                currentProgress: 0,
                xpReward: 900,
                expiresAt: expiryDate
            ),
            
            // Farm Challenges
            WeeklyChallenge(
                id: "weekly_farm_level",
                title: "Farm Developer",
                description: "Reach farm level 10",
                type: .farmLevel,
                targetValue: 10,
                currentProgress: 0,
                xpReward: 1500,
                expiresAt: expiryDate
            ),
            WeeklyChallenge(
                id: "weekly_building_upgrades",
                title: "Upgrade Master",
                description: "Upgrade 10 buildings",
                type: .buildingUpgrades,
                targetValue: 10,
                currentProgress: 0,
                xpReward: 700,
                expiresAt: expiryDate
            ),
            
            // Social Challenges
            WeeklyChallenge(
                id: "weekly_social_interactions",
                title: "Social Butterfly",
                description: "Visit 5 friends' farms",
                type: .socialInteractions,
                targetValue: 5,
                currentProgress: 0,
                xpReward: 600,
                expiresAt: expiryDate
            ),
            
            // Consistency Challenges
            WeeklyChallenge(
                id: "weekly_streak_days",
                title: "Dedicated Player",
                description: "Play for 7 consecutive days",
                type: .streakDays,
                targetValue: 7,
                currentProgress: 0,
                xpReward: 1000,
                expiresAt: expiryDate
            )
        ]
        
        // Return 3 random challenges
        return Array(allChallenges.shuffled().prefix(3))
    }
}

// MARK: - Challenge Progress Tracker
class ChallengeProgressTracker {
    
    static func updateChallengeProgress(
        challenges: inout [any BattlePassChallenge],
        source: XPSource,
        amount: Int = 1,
        metadata: [String: Any] = [:]
    ) {
        for i in 0..<challenges.count {
            if challenges[i].canProgress(from: source) && !challenges[i].isCompleted {
                
                // Special handling for specific challenge types
                let progressAmount = calculateProgressAmount(
                    for: challenges[i],
                    source: source,
                    baseAmount: amount,
                    metadata: metadata
                )
                
                challenges[i].updateProgress(amount: progressAmount)
                
                if challenges[i].isCompleted {
                    print("🏆 Challenge completed: \(challenges[i].title)")
                }
            }
        }
    }
    
    private static func calculateProgressAmount(
        for challenge: any BattlePassChallenge,
        source: XPSource,
        baseAmount: Int,
        metadata: [String: Any]
    ) -> Int {
        // Handle special cases where progress amount differs from base amount
        if let dailyChallenge = challenge as? DailyChallenge {
            switch dailyChallenge.type {
            case .coinsEarned:
                return metadata["coinsEarned"] as? Int ?? baseAmount
            case .perfectCombo:
                return metadata["comboCount"] as? Int ?? baseAmount
            case .farmHappiness:
                return Int((metadata["farmHappiness"] as? Float ?? 0) * 100)
            default:
                return baseAmount
            }
        }
        
        if let weeklyChallenge = challenge as? WeeklyChallenge {
            switch weeklyChallenge.type {
            case .totalScore:
                return metadata["scoreEarned"] as? Int ?? baseAmount
            case .perfectHits:
                return metadata["perfectHits"] as? Int ?? baseAmount
            case .farmLevel:
                return metadata["farmLevel"] as? Int ?? baseAmount
            default:
                return baseAmount
            }
        }
        
        return baseAmount
    }
}
