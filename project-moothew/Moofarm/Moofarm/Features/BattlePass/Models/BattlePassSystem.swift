//
//  BattlePassSystem.swift
//  Moofarm
//
//  Created by Project Moothew on 28/7/25.
//

import Foundation
import Combine

/// Advanced Battle Pass system with cross-system progression
/// Integrates rhythm game and farm activities into unified progression
class BattlePassSystem: ObservableObject {
    
    // MARK: - Published Properties
    @Published var currentSeason: BattlePassSeason?
    @Published var currentTier: Int = 0
    @Published var currentXP: Int = 0
    @Published var isPremiumUnlocked: Bool = false
    @Published var unlockedRewards: Set<String> = []
    @Published var dailyChallenges: [DailyChallenge] = []
    @Published var weeklyChallenges: [WeeklyChallenge] = []
    
    // MARK: - Services
    private let dataService: DataService
    private let gameManager: GameManager
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Constants
    private struct Constants {
        static let maxTier = 100
        static let xpPerTier = 1000
        static let dailyChallengeCount = 3
        static let weeklyChallengeCount = 3
        static let seasonDurationDays = 60
    }
    
    // MARK: - Initialization
    init(dataService: DataService = DataService.shared, gameManager: GameManager = GameManager.shared) {
        self.dataService = dataService
        self.gameManager = gameManager
        
        setupBindings()
        loadCurrentSeason()
        generateDailyChallenges()
        generateWeeklyChallenges()
    }
    
    // MARK: - Setup
    private func setupBindings() {
        // Listen for XP changes
        $currentXP
            .sink { [weak self] xp in
                self?.checkTierProgression(xp: xp)
            }
            .store(in: &cancellables)
    }
    
    // MARK: - Season Management
    private func loadCurrentSeason() {
        // Check if current season exists and is active
        if let season = getCurrentActiveSeason() {
            currentSeason = season
            loadPlayerProgress()
        } else {
            // Create new season
            createNewSeason()
        }
    }
    
    private func getCurrentActiveSeason() -> BattlePassSeason? {
        // In production, this would load from server/Core Data
        // For now, create a sample season
        let startDate = Calendar.current.date(byAdding: .day, value: -30, to: Date()) ?? Date()
        let endDate = Calendar.current.date(byAdding: .day, value: 30, to: Date()) ?? Date()
        
        return BattlePassSeason(
            seasonNumber: 1,
            name: "Farm Harmony",
            description: "Unite your rhythm skills with farm mastery!",
            startDate: startDate,
            endDate: endDate,
            tiers: generateSeasonTiers(),
            theme: "spring_harmony"
        )
    }
    
    private func createNewSeason() {
        let newSeason = BattlePassSeason(
            seasonNumber: 1,
            name: "Farm Harmony",
            description: "Unite your rhythm skills with farm mastery!",
            startDate: Date(),
            endDate: Calendar.current.date(byAdding: .day, value: Constants.seasonDurationDays, to: Date()) ?? Date(),
            tiers: generateSeasonTiers(),
            theme: "spring_harmony"
        )
        
        currentSeason = newSeason
        currentTier = 0
        currentXP = 0
        
        print("🎯 Created new Battle Pass season: \(newSeason.name)")
    }
    
    private func generateSeasonTiers() -> [BattlePassTier] {
        var tiers: [BattlePassTier] = []
        
        for tier in 1...Constants.maxTier {
            let xpRequired = tier * Constants.xpPerTier
            
            // Generate rewards based on tier
            let freeReward = generateFreeReward(for: tier)
            let premiumReward = generatePremiumReward(for: tier)
            
            tiers.append(BattlePassTier(
                tier: tier,
                xpRequired: xpRequired,
                freeReward: freeReward,
                premiumReward: premiumReward
            ))
        }
        
        return tiers
    }
    
    private func generateFreeReward(for tier: Int) -> BattlePassTier.BattlePassReward? {
        switch tier {
        case let t where t % 10 == 0: // Every 10th tier
            return BattlePassTier.BattlePassReward(
                type: .animal,
                amount: 1,
                description: "Unlock new farm animal"
            )
        case let t where t % 5 == 0: // Every 5th tier
            return BattlePassTier.BattlePassReward(
                type: .coins,
                amount: tier * 100,
                description: "\(tier * 100) coins"
            )
        default:
            return BattlePassTier.BattlePassReward(
                type: .coins,
                amount: tier * 50,
                description: "\(tier * 50) coins"
            )
        }
    }
    
    private func generatePremiumReward(for tier: Int) -> BattlePassTier.BattlePassReward? {
        switch tier {
        case let t where t % 20 == 0: // Every 20th tier
            return BattlePassTier.BattlePassReward(
                type: .building,
                amount: 1,
                description: "Exclusive premium building"
            )
        case let t where t % 10 == 0: // Every 10th tier
            return BattlePassTier.BattlePassReward(
                type: .gems,
                amount: tier * 10,
                description: "\(tier * 10) gems"
            )
        case let t where t % 5 == 0: // Every 5th tier
            return BattlePassTier.BattlePassReward(
                type: .decoration,
                amount: 1,
                description: "Premium farm decoration"
            )
        default:
            return BattlePassTier.BattlePassReward(
                type: .coins,
                amount: tier * 100,
                description: "\(tier * 100) coins"
            )
        }
    }
    
    // MARK: - XP Management
    func addXP(_ amount: Int, source: XPSource) {
        let oldXP = currentXP
        currentXP += amount
        
        // Check for tier progression
        checkTierProgression(xp: currentXP)
        
        // Update challenges
        updateChallengeProgress(xpGained: amount, source: source)
        
        print("🎯 Battle Pass XP: +\(amount) from \(source.rawValue) (Total: \(currentXP))")
    }
    
    private func checkTierProgression(xp: Int) {
        let newTier = min(xp / Constants.xpPerTier, Constants.maxTier)
        
        if newTier > currentTier {
            let tiersGained = newTier - currentTier
            currentTier = newTier
            
            // Unlock rewards for gained tiers
            for tier in (currentTier - tiersGained + 1)...currentTier {
                unlockTierRewards(tier: tier)
            }
            
            print("🎉 Battle Pass tier up! Now tier \(currentTier)")
        }
    }
    
    private func unlockTierRewards(tier: Int) {
        guard let season = currentSeason,
              tier <= season.tiers.count else { return }
        
        let tierData = season.tiers[tier - 1]
        
        // Unlock free reward
        if let freeReward = tierData.freeReward {
            applyReward(freeReward, tier: tier, isPremium: false)
        }
        
        // Unlock premium reward if player has premium
        if isPremiumUnlocked, let premiumReward = tierData.premiumReward {
            applyReward(premiumReward, tier: tier, isPremium: true)
        }
    }
    
    private func applyReward(_ reward: BattlePassTier.BattlePassReward, tier: Int, isPremium: Bool) {
        guard let player = gameManager.currentPlayer else { return }
        
        let rewardId = "tier_\(tier)_\(isPremium ? "premium" : "free")"
        unlockedRewards.insert(rewardId)
        
        switch reward.type {
        case .coins:
            player.add(coins: Int32(reward.amount))
        case .gems:
            player.add(gems: Int32(reward.amount))
        case .animal:
            unlockRandomAnimal()
        case .building:
            unlockPremiumBuilding()
        case .decoration:
            unlockPremiumDecoration()
        case .song:
            unlockPremiumSong()
        }
        
        print("🎁 Unlocked \(isPremium ? "premium" : "free") reward: \(reward.description)")
    }
    
    // MARK: - Challenge System
    private func generateDailyChallenges() {
        dailyChallenges = [
            DailyChallenge(
                id: "daily_rhythm_1",
                title: "Rhythm Master",
                description: "Complete 3 rhythm songs",
                type: .rhythmSongs,
                targetValue: 3,
                currentProgress: 0,
                xpReward: 200,
                expiresAt: Calendar.current.date(byAdding: .day, value: 1, to: Date()) ?? Date()
            ),
            DailyChallenge(
                id: "daily_farm_1",
                title: "Builder",
                description: "Place 2 buildings on your farm",
                type: .buildingsPlaced,
                targetValue: 2,
                currentProgress: 0,
                xpReward: 150,
                expiresAt: Calendar.current.date(byAdding: .day, value: 1, to: Date()) ?? Date()
            ),
            DailyChallenge(
                id: "daily_combo_1",
                title: "Perfect Combo",
                description: "Achieve a 50+ combo in rhythm game",
                type: .perfectCombo,
                targetValue: 50,
                currentProgress: 0,
                xpReward: 300,
                expiresAt: Calendar.current.date(byAdding: .day, value: 1, to: Date()) ?? Date()
            )
        ]
    }
    
    private func generateWeeklyChallenges() {
        weeklyChallenges = [
            WeeklyChallenge(
                id: "weekly_master_1",
                title: "Farm & Rhythm Master",
                description: "Complete 20 rhythm songs and reach farm level 5",
                type: .crossSystem,
                targetValue: 1,
                currentProgress: 0,
                xpReward: 1000,
                expiresAt: Calendar.current.date(byAdding: .weekOfYear, value: 1, to: Date()) ?? Date()
            ),
            WeeklyChallenge(
                id: "weekly_collector_1",
                title: "Animal Collector",
                description: "Unlock 5 new animals",
                type: .animalsUnlocked,
                targetValue: 5,
                currentProgress: 0,
                xpReward: 800,
                expiresAt: Calendar.current.date(byAdding: .weekOfYear, value: 1, to: Date()) ?? Date()
            ),
            WeeklyChallenge(
                id: "weekly_score_1",
                title: "High Scorer",
                description: "Achieve total score of 500,000 in rhythm games",
                type: .totalScore,
                targetValue: 500000,
                currentProgress: 0,
                xpReward: 1200,
                expiresAt: Calendar.current.date(byAdding: .weekOfYear, value: 1, to: Date()) ?? Date()
            )
        ]
    }
    
    private func updateChallengeProgress(xpGained: Int, source: XPSource) {
        // Update daily challenges
        for i in 0..<dailyChallenges.count {
            if dailyChallenges[i].canProgress(from: source) {
                dailyChallenges[i].updateProgress(amount: 1)
                
                if dailyChallenges[i].isCompleted && !dailyChallenges[i].isRewardClaimed {
                    claimChallengeReward(&dailyChallenges[i])
                }
            }
        }
        
        // Update weekly challenges
        for i in 0..<weeklyChallenges.count {
            if weeklyChallenges[i].canProgress(from: source) {
                weeklyChallenges[i].updateProgress(amount: xpGained)
                
                if weeklyChallenges[i].isCompleted && !weeklyChallenges[i].isRewardClaimed {
                    claimChallengeReward(&weeklyChallenges[i])
                }
            }
        }
    }
    
    private func claimChallengeReward<T: BattlePassChallenge>(_ challenge: inout T) {
        challenge.isRewardClaimed = true
        addXP(challenge.xpReward, source: .challenge)
        print("🏆 Challenge completed: \(challenge.title) (+\(challenge.xpReward) XP)")
    }
    
    // MARK: - Premium System
    func unlockPremium() -> Bool {
        // In production, this would involve in-app purchase
        isPremiumUnlocked = true
        
        // Retroactively unlock premium rewards for current tier
        for tier in 1...currentTier {
            if let season = currentSeason,
               tier <= season.tiers.count,
               let premiumReward = season.tiers[tier - 1].premiumReward {
                applyReward(premiumReward, tier: tier, isPremium: true)
            }
        }
        
        print("💎 Premium Battle Pass unlocked!")
        return true
    }
    
    // MARK: - Reward Helpers
    private func unlockRandomAnimal() {
        // Find a locked animal to unlock
        let request = Animal.fetchRequest()
        request.predicate = NSPredicate(format: "isUnlocked == NO")
        request.fetchLimit = 1
        
        do {
            let lockedAnimals = try dataService.viewContext.fetch(request)
            if let animal = lockedAnimals.first {
                animal.unlock()
                print("🐾 Battle Pass unlocked animal: \(animal.displayName)")
            }
        } catch {
            print("❌ Failed to unlock animal: \(error)")
        }
    }
    
    private func unlockPremiumBuilding() {
        // Unlock special premium building
        print("🏗️ Premium building unlocked!")
        // Implementation would add special building to available buildings
    }
    
    private func unlockPremiumDecoration() {
        // Unlock special decoration
        print("🌸 Premium decoration unlocked!")
        // Implementation would add decoration to inventory
    }
    
    private func unlockPremiumSong() {
        // Unlock special song
        print("🎵 Premium song unlocked!")
        // Implementation would unlock special song
    }
    
    // MARK: - Player Progress
    private func loadPlayerProgress() {
        guard let player = gameManager.currentPlayer else { return }
        
        currentXP = Int(player.battlePassXP)
        currentTier = currentXP / Constants.xpPerTier
        isPremiumUnlocked = player.hasPremiumBattlePass
        
        // Load unlocked rewards (would be stored in Core Data)
        // For now, calculate based on current tier
        for tier in 1...currentTier {
            let freeRewardId = "tier_\(tier)_free"
            unlockedRewards.insert(freeRewardId)
            
            if isPremiumUnlocked {
                let premiumRewardId = "tier_\(tier)_premium"
                unlockedRewards.insert(premiumRewardId)
            }
        }
    }
    
    // MARK: - Public Interface
    func getProgressToNextTier() -> Float {
        let xpInCurrentTier = currentXP % Constants.xpPerTier
        return Float(xpInCurrentTier) / Float(Constants.xpPerTier)
    }
    
    func getXPToNextTier() -> Int {
        let xpInCurrentTier = currentXP % Constants.xpPerTier
        return Constants.xpPerTier - xpInCurrentTier
    }
    
    func getDaysRemaining() -> Int {
        return currentSeason?.daysRemaining ?? 0
    }
    
    func isRewardUnlocked(tier: Int, isPremium: Bool) -> Bool {
        let rewardId = "tier_\(tier)_\(isPremium ? "premium" : "free")"
        return unlockedRewards.contains(rewardId)
    }
}

// MARK: - XP Source Enum
enum XPSource: String, CaseIterable {
    case rhythmGame = "rhythm_game"
    case farmBuilding = "farm_building"
    case animalCare = "animal_care"
    case dailyLogin = "daily_login"
    case challenge = "challenge"
    case achievement = "achievement"
    case social = "social"
    
    var displayName: String {
        switch self {
        case .rhythmGame: return "Rhythm Game"
        case .farmBuilding: return "Farm Building"
        case .animalCare: return "Animal Care"
        case .dailyLogin: return "Daily Login"
        case .challenge: return "Challenge"
        case .achievement: return "Achievement"
        case .social: return "Social"
        }
    }
}
