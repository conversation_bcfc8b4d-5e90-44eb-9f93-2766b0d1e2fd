//
//  BattlePassView.swift
//  Moofarm
//
//  Created by Project Moothew on 28/7/25.
//

import SwiftUI

/// Battle Pass main interface with tier progression and rewards
/// Shows current season, challenges, and reward tracks
struct BattlePassView: View {
    
    // MARK: - Environment
    @EnvironmentObject private var gameManager: GameManager
    @EnvironmentObject private var audioService: AudioService
    @Environment(\.dismiss) private var dismiss
    
    // MARK: - State
    @StateObject private var battlePassSystem = BattlePassSystem()
    @State private var selectedTab: BattlePassTab = .tiers
    @State private var showPremiumPurchase = false
    @State private var selectedTier: Int?
    
    // MARK: - Body
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Header
                battlePassHeader
                
                // Tab Selection
                tabSelector
                
                // Content
                TabView(selection: $selectedTab) {
                    // Tier Progression
                    tierProgressionView
                        .tag(BattlePassTab.tiers)
                    
                    // Challenges
                    challengesView
                        .tag(BattlePassTab.challenges)
                    
                    // Rewards
                    rewardsView
                        .tag(BattlePassTab.rewards)
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
            }
            .navigationBarHidden(true)
            .background(
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color(red: 0.1, green: 0.1, blue: 0.3),
                        Color(red: 0.2, green: 0.1, blue: 0.4)
                    ]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            )
        }
        .sheet(isPresented: $showPremiumPurchase) {
            PremiumPurchaseView(battlePassSystem: battlePassSystem)
        }
    }
    
    // MARK: - Header
    private var battlePassHeader: some View {
        VStack(spacing: 15) {
            // Top Bar
            HStack {
                Button(action: { dismiss() }) {
                    Image(systemName: "arrow.left")
                        .font(.title2)
                        .foregroundColor(.white)
                }
                
                Spacer()
                
                VStack {
                    Text(battlePassSystem.currentSeason?.name ?? "Battle Pass")
                        .font(.headline.bold())
                        .foregroundColor(.white)
                    
                    Text("\(battlePassSystem.getDaysRemaining()) days left")
                        .font(.caption)
                        .foregroundColor(.gray)
                }
                
                Spacer()
                
                if !battlePassSystem.isPremiumUnlocked {
                    Button("Premium") {
                        showPremiumPurchase = true
                    }
                    .font(.caption.bold())
                    .foregroundColor(.black)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(Color.yellow)
                    .cornerRadius(15)
                }
            }
            .padding(.horizontal)
            .padding(.top, 50)
            
            // Progress Bar
            VStack(spacing: 8) {
                HStack {
                    Text("Tier \(battlePassSystem.currentTier)")
                        .font(.title2.bold())
                        .foregroundColor(.white)
                    
                    Spacer()
                    
                    Text("\(battlePassSystem.getXPToNextTier()) XP to next tier")
                        .font(.caption)
                        .foregroundColor(.gray)
                }
                
                ProgressView(value: battlePassSystem.getProgressToNextTier())
                    .progressViewStyle(LinearProgressViewStyle(tint: .yellow))
                    .scaleEffect(x: 1, y: 3, anchor: .center)
            }
            .padding(.horizontal)
        }
        .padding(.bottom, 20)
    }
    
    // MARK: - Tab Selector
    private var tabSelector: some View {
        HStack(spacing: 0) {
            ForEach(BattlePassTab.allCases, id: \.self) { tab in
                Button(action: { selectedTab = tab }) {
                    VStack(spacing: 4) {
                        Text(tab.icon)
                            .font(.title2)
                        
                        Text(tab.title)
                            .font(.caption.bold())
                    }
                    .foregroundColor(selectedTab == tab ? .yellow : .gray)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 12)
                }
            }
        }
        .background(Color.black.opacity(0.3))
    }
    
    // MARK: - Tier Progression
    private var tierProgressionView: some View {
        ScrollView {
            LazyVStack(spacing: 12) {
                ForEach(1...min(battlePassSystem.currentTier + 5, 100), id: \.self) { tier in
                    TierRowView(
                        tier: tier,
                        battlePassSystem: battlePassSystem,
                        isSelected: selectedTier == tier,
                        onTap: {
                            selectedTier = selectedTier == tier ? nil : tier
                        }
                    )
                }
            }
            .padding()
        }
    }
    
    // MARK: - Challenges
    private var challengesView: some View {
        ScrollView {
            VStack(spacing: 20) {
                // Daily Challenges
                VStack(alignment: .leading, spacing: 12) {
                    Text("Daily Challenges")
                        .font(.headline.bold())
                        .foregroundColor(.white)
                    
                    ForEach(battlePassSystem.dailyChallenges) { challenge in
                        ChallengeRowView(challenge: challenge)
                    }
                }
                
                // Weekly Challenges
                VStack(alignment: .leading, spacing: 12) {
                    Text("Weekly Challenges")
                        .font(.headline.bold())
                        .foregroundColor(.white)
                    
                    ForEach(battlePassSystem.weeklyChallenges) { challenge in
                        ChallengeRowView(challenge: challenge)
                    }
                }
            }
            .padding()
        }
    }
    
    // MARK: - Rewards
    private var rewardsView: some View {
        ScrollView {
            VStack(spacing: 20) {
                // Unlocked Rewards
                VStack(alignment: .leading, spacing: 12) {
                    Text("Unlocked Rewards")
                        .font(.headline.bold())
                        .foregroundColor(.white)
                    
                    LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 3), spacing: 12) {
                        ForEach(getUnlockedRewards(), id: \.tier) { reward in
                            RewardCard(reward: reward, isUnlocked: true)
                        }
                    }
                }
                
                // Upcoming Rewards
                VStack(alignment: .leading, spacing: 12) {
                    Text("Upcoming Rewards")
                        .font(.headline.bold())
                        .foregroundColor(.white)
                    
                    LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 3), spacing: 12) {
                        ForEach(getUpcomingRewards(), id: \.tier) { reward in
                            RewardCard(reward: reward, isUnlocked: false)
                        }
                    }
                }
            }
            .padding()
        }
    }
    
    // MARK: - Helper Methods
    private func getUnlockedRewards() -> [RewardInfo] {
        guard let season = battlePassSystem.currentSeason else { return [] }
        
        var rewards: [RewardInfo] = []
        
        for tier in 1...battlePassSystem.currentTier {
            if tier <= season.tiers.count {
                let tierData = season.tiers[tier - 1]
                
                if let freeReward = tierData.freeReward {
                    rewards.append(RewardInfo(
                        tier: tier,
                        reward: freeReward,
                        isPremium: false
                    ))
                }
                
                if battlePassSystem.isPremiumUnlocked,
                   let premiumReward = tierData.premiumReward {
                    rewards.append(RewardInfo(
                        tier: tier,
                        reward: premiumReward,
                        isPremium: true
                    ))
                }
            }
        }
        
        return rewards
    }
    
    private func getUpcomingRewards() -> [RewardInfo] {
        guard let season = battlePassSystem.currentSeason else { return [] }
        
        var rewards: [RewardInfo] = []
        let nextTiers = (battlePassSystem.currentTier + 1)...(battlePassSystem.currentTier + 10)
        
        for tier in nextTiers {
            if tier <= season.tiers.count {
                let tierData = season.tiers[tier - 1]
                
                if let freeReward = tierData.freeReward {
                    rewards.append(RewardInfo(
                        tier: tier,
                        reward: freeReward,
                        isPremium: false
                    ))
                }
                
                if let premiumReward = tierData.premiumReward {
                    rewards.append(RewardInfo(
                        tier: tier,
                        reward: premiumReward,
                        isPremium: true
                    ))
                }
            }
        }
        
        return rewards
    }
}

// MARK: - Supporting Views
struct TierRowView: View {
    let tier: Int
    let battlePassSystem: BattlePassSystem
    let isSelected: Bool
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            HStack(spacing: 15) {
                // Tier Number
                Text("\(tier)")
                    .font(.title2.bold())
                    .foregroundColor(isUnlocked ? .yellow : .gray)
                    .frame(width: 40)
                
                // Free Reward
                RewardIcon(
                    reward: freeReward,
                    isUnlocked: isUnlocked,
                    isPremium: false
                )
                
                // Premium Reward
                RewardIcon(
                    reward: premiumReward,
                    isUnlocked: isUnlocked && battlePassSystem.isPremiumUnlocked,
                    isPremium: true
                )
                
                Spacer()
                
                // Status
                if isUnlocked {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                } else if tier == battlePassSystem.currentTier + 1 {
                    Text("Next")
                        .font(.caption.bold())
                        .foregroundColor(.yellow)
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(isSelected ? Color.white.opacity(0.1) : Color.black.opacity(0.3))
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private var isUnlocked: Bool {
        return tier <= battlePassSystem.currentTier
    }
    
    private var freeReward: BattlePassTier.BattlePassReward? {
        guard let season = battlePassSystem.currentSeason,
              tier <= season.tiers.count else { return nil }
        return season.tiers[tier - 1].freeReward
    }
    
    private var premiumReward: BattlePassTier.BattlePassReward? {
        guard let season = battlePassSystem.currentSeason,
              tier <= season.tiers.count else { return nil }
        return season.tiers[tier - 1].premiumReward
    }
}

struct RewardIcon: View {
    let reward: BattlePassTier.BattlePassReward?
    let isUnlocked: Bool
    let isPremium: Bool
    
    var body: some View {
        ZStack {
            RoundedRectangle(cornerRadius: 8)
                .fill(isPremium ? Color.yellow.opacity(0.2) : Color.gray.opacity(0.2))
                .frame(width: 50, height: 50)
            
            if let reward = reward {
                Text(reward.type.emoji)
                    .font(.title2)
                    .opacity(isUnlocked ? 1.0 : 0.5)
            } else {
                Text("—")
                    .foregroundColor(.gray)
            }
            
            if isPremium {
                VStack {
                    Spacer()
                    HStack {
                        Spacer()
                        Image(systemName: "crown.fill")
                            .font(.caption2)
                            .foregroundColor(.yellow)
                    }
                }
                .frame(width: 50, height: 50)
            }
        }
    }
}

struct ChallengeRowView: View {
    let challenge: any BattlePassChallenge
    
    var body: some View {
        HStack(spacing: 12) {
            // Icon
            Text(challengeIcon)
                .font(.title2)
            
            // Info
            VStack(alignment: .leading, spacing: 4) {
                Text(challenge.title)
                    .font(.headline)
                    .foregroundColor(.white)
                
                Text(challenge.description)
                    .font(.caption)
                    .foregroundColor(.gray)
                
                // Progress Bar
                ProgressView(value: progressValue)
                    .progressViewStyle(LinearProgressViewStyle(tint: .blue))
                    .scaleEffect(x: 1, y: 2, anchor: .center)
            }
            
            Spacer()
            
            // Reward
            VStack {
                Text("+\(challenge.xpReward)")
                    .font(.caption.bold())
                    .foregroundColor(.yellow)
                
                Text("XP")
                    .font(.caption2)
                    .foregroundColor(.gray)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(challenge.isCompleted ? Color.green.opacity(0.2) : Color.black.opacity(0.3))
        )
    }
    
    private var challengeIcon: String {
        if let daily = challenge as? DailyChallenge {
            return daily.type.icon
        } else if let weekly = challenge as? WeeklyChallenge {
            return weekly.type.icon
        }
        return "🎯"
    }
    
    private var progressValue: Float {
        return Float(challenge.currentProgress) / Float(challenge.targetValue)
    }
}

struct RewardCard: View {
    let reward: RewardInfo
    let isUnlocked: Bool
    
    var body: some View {
        VStack(spacing: 8) {
            Text(reward.reward.type.emoji)
                .font(.title)
                .opacity(isUnlocked ? 1.0 : 0.5)
            
            Text("Tier \(reward.tier)")
                .font(.caption2)
                .foregroundColor(.gray)
            
            Text(reward.reward.description)
                .font(.caption)
                .foregroundColor(.white)
                .multilineTextAlignment(.center)
                .lineLimit(2)
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 10)
                .fill(reward.isPremium ? Color.yellow.opacity(0.2) : Color.gray.opacity(0.2))
        )
        .overlay(
            RoundedRectangle(cornerRadius: 10)
                .stroke(reward.isPremium ? Color.yellow : Color.clear, lineWidth: 1)
        )
    }
}

// MARK: - Supporting Types
enum BattlePassTab: String, CaseIterable {
    case tiers = "tiers"
    case challenges = "challenges"
    case rewards = "rewards"
    
    var title: String {
        switch self {
        case .tiers: return "Tiers"
        case .challenges: return "Challenges"
        case .rewards: return "Rewards"
        }
    }
    
    var icon: String {
        switch self {
        case .tiers: return "🎯"
        case .challenges: return "🏆"
        case .rewards: return "🎁"
        }
    }
}

struct RewardInfo {
    let tier: Int
    let reward: BattlePassTier.BattlePassReward
    let isPremium: Bool
}

// MARK: - Premium Purchase View
struct PremiumPurchaseView: View {
    let battlePassSystem: BattlePassSystem
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack(spacing: 30) {
                Text("💎")
                    .font(.system(size: 80))
                
                Text("Premium Battle Pass")
                    .font(.title.bold())
                
                Text("Unlock premium rewards and get more value from your gameplay!")
                    .multilineTextAlignment(.center)
                    .foregroundColor(.secondary)
                
                VStack(spacing: 15) {
                    FeatureRow(icon: "🎁", text: "Exclusive premium rewards")
                    FeatureRow(icon: "⚡", text: "2x XP boost for all activities")
                    FeatureRow(icon: "🏗️", text: "Special buildings and decorations")
                    FeatureRow(icon: "🐾", text: "Rare animals and skins")
                }
                
                Button("Purchase for $9.99") {
                    _ = battlePassSystem.unlockPremium()
                    dismiss()
                }
                .font(.headline.bold())
                .foregroundColor(.white)
                .frame(width: 250, height: 50)
                .background(Color.blue)
                .cornerRadius(25)
                
                Button("Maybe Later") {
                    dismiss()
                }
                .foregroundColor(.secondary)
            }
            .padding()
            .navigationTitle("Premium")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Close") { dismiss() }
                }
            }
        }
    }
}

struct FeatureRow: View {
    let icon: String
    let text: String
    
    var body: some View {
        HStack {
            Text(icon)
                .font(.title2)
            
            Text(text)
                .font(.headline)
            
            Spacer()
        }
    }
}

// MARK: - Preview
#Preview {
    BattlePassView()
        .environmentObject(GameManager.shared)
        .environmentObject(AudioService.shared)
}
