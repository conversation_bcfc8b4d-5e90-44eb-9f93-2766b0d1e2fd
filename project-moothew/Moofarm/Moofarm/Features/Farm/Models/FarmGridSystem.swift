//
//  FarmGridSystem.swift
//  Moofarm
//
//  Created by Project Moothew on 28/7/25.
//

import Foundation
import SwiftUI

/// Grid system for farm building placement
/// Handles grid calculations, collision detection, and placement validation
class FarmGridSystem: ObservableObject {
    
    // MARK: - Constants
    struct Constants {
        static let gridSize: CGFloat = 60.0
        static let defaultFarmSize = CGSize(width: 12, height: 12)
        static let minFarmSize = CGSize(width: 8, height: 8)
        static let maxFarmSize = CGSize(width: 20, height: 20)
    }
    
    // MARK: - Properties
    @Published var farmSize: CGSize
    @Published var occupiedCells: Set<GridPosition> = []
    @Published var selectedPosition: GridPosition?
    @Published var previewBuilding: BuildingConfiguration.BuildingType?
    
    private var placedBuildings: [GridPosition: PlacedBuildingData] = [:]
    
    // MARK: - Initialization
    init(farmSize: CGSize = Constants.defaultFarmSize) {
        self.farmSize = farmSize
    }
    
    // MARK: - Grid Calculations
    
    /// Convert screen point to grid position
    func screenToGrid(_ point: CGPoint, in geometry: GeometryProxy) -> GridPosition {
        let gridOriginX = (geometry.size.width - (farmSize.width * Constants.gridSize)) / 2
        let gridOriginY = (geometry.size.height - (farmSize.height * Constants.gridSize)) / 2
        
        let relativeX = point.x - gridOriginX
        let relativeY = point.y - gridOriginY
        
        let gridX = Int(relativeX / Constants.gridSize)
        let gridY = Int(relativeY / Constants.gridSize)
        
        return GridPosition(
            x: max(0, min(Int(farmSize.width) - 1, gridX)),
            y: max(0, min(Int(farmSize.height) - 1, gridY))
        )
    }
    
    /// Convert grid position to screen point
    func gridToScreen(_ position: GridPosition, in geometry: GeometryProxy) -> CGPoint {
        let gridOriginX = (geometry.size.width - (farmSize.width * Constants.gridSize)) / 2
        let gridOriginY = (geometry.size.height - (farmSize.height * Constants.gridSize)) / 2
        
        return CGPoint(
            x: gridOriginX + (CGFloat(position.x) * Constants.gridSize) + (Constants.gridSize / 2),
            y: gridOriginY + (CGFloat(position.y) * Constants.gridSize) + (Constants.gridSize / 2)
        )
    }
    
    /// Get rect for grid position
    func rectForPosition(_ position: GridPosition, size: CGSize = CGSize(width: 1, height: 1)) -> CGRect {
        return CGRect(
            x: CGFloat(position.x) * Constants.gridSize,
            y: CGFloat(position.y) * Constants.gridSize,
            width: size.width * Constants.gridSize,
            height: size.height * Constants.gridSize
        )
    }
    
    // MARK: - Placement Validation
    
    /// Check if a building can be placed at the given position
    func canPlaceBuilding(_ buildingType: BuildingConfiguration.BuildingType, at position: GridPosition) -> Bool {
        guard let config = getBuildingConfig(buildingType) else { return false }
        
        // Check bounds
        let endX = position.x + Int(config.size.width)
        let endY = position.y + Int(config.size.height)
        
        if endX > Int(farmSize.width) || endY > Int(farmSize.height) {
            return false
        }
        
        // Check for overlaps
        for x in position.x..<endX {
            for y in position.y..<endY {
                let checkPosition = GridPosition(x: x, y: y)
                if occupiedCells.contains(checkPosition) {
                    return false
                }
            }
        }
        
        return true
    }
    
    /// Get all positions that would be occupied by a building
    func getOccupiedPositions(for buildingType: BuildingConfiguration.BuildingType, at position: GridPosition) -> [GridPosition] {
        guard let config = getBuildingConfig(buildingType) else { return [] }
        
        var positions: [GridPosition] = []
        let endX = position.x + Int(config.size.width)
        let endY = position.y + Int(config.size.height)
        
        for x in position.x..<endX {
            for y in position.y..<endY {
                positions.append(GridPosition(x: x, y: y))
            }
        }
        
        return positions
    }
    
    /// Check if position is valid (within bounds)
    func isValidPosition(_ position: GridPosition) -> Bool {
        return position.x >= 0 && position.x < Int(farmSize.width) &&
               position.y >= 0 && position.y < Int(farmSize.height)
    }
    
    // MARK: - Building Management
    
    /// Place a building at the specified position
    func placeBuilding(_ buildingType: BuildingConfiguration.BuildingType, at position: GridPosition) -> Bool {
        guard canPlaceBuilding(buildingType, at: position) else { return false }
        
        let occupiedPositions = getOccupiedPositions(for: buildingType, at: position)
        
        // Mark cells as occupied
        for pos in occupiedPositions {
            occupiedCells.insert(pos)
        }
        
        // Store building data
        let buildingData = PlacedBuildingData(
            type: buildingType,
            position: position,
            level: 1,
            placedAt: Date()
        )
        placedBuildings[position] = buildingData
        
        return true
    }
    
    /// Remove building at position
    func removeBuilding(at position: GridPosition) -> Bool {
        guard let buildingData = placedBuildings[position] else { return false }
        
        let occupiedPositions = getOccupiedPositions(for: buildingData.type, at: position)
        
        // Free up cells
        for pos in occupiedPositions {
            occupiedCells.remove(pos)
        }
        
        // Remove building data
        placedBuildings.removeValue(forKey: position)
        
        return true
    }
    
    /// Get building at position
    func getBuildingAt(_ position: GridPosition) -> PlacedBuildingData? {
        // Check if this position contains a building (could be part of a larger building)
        for (buildingPosition, buildingData) in placedBuildings {
            let occupiedPositions = getOccupiedPositions(for: buildingData.type, at: buildingPosition)
            if occupiedPositions.contains(position) {
                return buildingData
            }
        }
        return nil
    }
    
    /// Get all placed buildings
    func getAllBuildings() -> [PlacedBuildingData] {
        return Array(placedBuildings.values)
    }
    
    // MARK: - Preview System
    
    /// Set preview building for placement
    func setPreviewBuilding(_ buildingType: BuildingConfiguration.BuildingType?, at position: GridPosition?) {
        previewBuilding = buildingType
        selectedPosition = position
    }
    
    /// Clear preview
    func clearPreview() {
        previewBuilding = nil
        selectedPosition = nil
    }
    
    /// Check if preview placement is valid
    var isPreviewValid: Bool {
        guard let buildingType = previewBuilding,
              let position = selectedPosition else { return false }
        return canPlaceBuilding(buildingType, at: position)
    }
    
    // MARK: - Farm Expansion
    
    /// Expand farm size (if player has unlocked expansion)
    func expandFarm(to newSize: CGSize) -> Bool {
        guard newSize.width >= Constants.minFarmSize.width &&
              newSize.height >= Constants.minFarmSize.height &&
              newSize.width <= Constants.maxFarmSize.width &&
              newSize.height <= Constants.maxFarmSize.height else {
            return false
        }
        
        // Check if any buildings would be outside new bounds
        for buildingData in placedBuildings.values {
            let config = getBuildingConfig(buildingData.type)!
            let endX = buildingData.position.x + Int(config.size.width)
            let endY = buildingData.position.y + Int(config.size.height)
            
            if endX > Int(newSize.width) || endY > Int(newSize.height) {
                return false // Cannot shrink if buildings would be outside
            }
        }
        
        farmSize = newSize
        return true
    }
    
    // MARK: - Serialization
    
    /// Export farm layout to data
    func exportLayout() -> FarmLayout {
        let buildings = placedBuildings.values.map { buildingData in
            FarmLayout.PlacedBuilding(
                type: buildingData.type,
                position: buildingData.position,
                level: buildingData.level
            )
        }
        
        return FarmLayout(
            buildings: buildings,
            animals: [], // Will be populated by animal system
            decorations: [], // Will be populated by decoration system
            gridSize: farmSize
        )
    }
    
    /// Import farm layout from data
    func importLayout(_ layout: FarmLayout) {
        // Clear current layout
        occupiedCells.removeAll()
        placedBuildings.removeAll()
        
        // Set farm size
        farmSize = layout.gridSize
        
        // Place buildings
        for building in layout.buildings {
            let buildingData = PlacedBuildingData(
                type: building.type,
                position: building.position,
                level: building.level,
                placedAt: building.placedAt
            )
            
            placedBuildings[building.position] = buildingData
            
            // Mark cells as occupied
            let occupiedPositions = getOccupiedPositions(for: building.type, at: building.position)
            for pos in occupiedPositions {
                occupiedCells.insert(pos)
            }
        }
    }
    
    // MARK: - Helper Methods
    
    private func getBuildingConfig(_ type: BuildingConfiguration.BuildingType) -> BuildingConfiguration? {
        return BuildingConfiguration.defaultBuildings.first { $0.type == type }
    }
}

// MARK: - Supporting Data Structures
struct PlacedBuildingData: Identifiable, Hashable {
    let id = UUID()
    let type: BuildingConfiguration.BuildingType
    let position: GridPosition
    var level: Int
    let placedAt: Date
    
    func hash(into hasher: inout Hasher) {
        hasher.combine(id)
    }
    
    static func == (lhs: PlacedBuildingData, rhs: PlacedBuildingData) -> Bool {
        return lhs.id == rhs.id
    }
}

// MARK: - Grid Position Extensions
extension GridPosition {
    /// Get adjacent positions (4-directional)
    var adjacentPositions: [GridPosition] {
        return [
            GridPosition(x: x - 1, y: y),
            GridPosition(x: x + 1, y: y),
            GridPosition(x: x, y: y - 1),
            GridPosition(x: x, y: y + 1)
        ]
    }
    
    /// Get all positions in a radius
    func positionsInRadius(_ radius: Int) -> [GridPosition] {
        var positions: [GridPosition] = []
        
        for dx in -radius...radius {
            for dy in -radius...radius {
                let distance = abs(dx) + abs(dy) // Manhattan distance
                if distance <= radius {
                    positions.append(GridPosition(x: x + dx, y: y + dy))
                }
            }
        }
        
        return positions
    }
}
