//
//  FarmInfoView.swift
//  Moofarm
//
//  Created by Project Moothew on 28/7/25.
//

import SwiftUI

/// Detailed farm information and statistics view
/// Shows farm stats, rhythm bonuses, and progression info
struct FarmInfoView: View {
    
    // MARK: - Properties
    let farm: Farm?
    
    // MARK: - Environment
    @EnvironmentObject private var gameManager: GameManager
    @Environment(\.dismiss) private var dismiss
    
    // MARK: - State
    @State private var showDailyRewards = false
    @State private var dailyRewards: DailyRewards?
    
    // MARK: - Body
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // Farm Header
                    farmHeaderSection
                    
                    // Statistics Cards
                    statisticsSection
                    
                    // Rhythm Integration
                    rhythmBonusSection
                    
                    // Buildings Overview
                    buildingsSection
                    
                    // Animals Overview
                    animalsSection
                    
                    // Daily Rewards
                    dailyRewardsSection
                    
                    Spacer(minLength: 20)
                }
                .padding()
            }
            .navigationTitle("Farm Info")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
        .sheet(isPresented: $showDailyRewards) {
            if let rewards = dailyRewards {
                DailyRewardsView(rewards: rewards)
            }
        }
    }
    
    // MARK: - Farm Header
    private var farmHeaderSection: some View {
        VStack(spacing: 15) {
            // Farm Name and Level
            VStack(spacing: 5) {
                Text(farm?.farmName ?? "Unknown Farm")
                    .font(.title.bold())
                
                Text("Level \(farm?.level ?? 1)")
                    .font(.title2)
                    .foregroundColor(.secondary)
            }
            
            // Happiness Bar
            VStack(spacing: 8) {
                HStack {
                    Text("Farm Happiness")
                        .font(.headline)
                    
                    Spacer()
                    
                    Text("\(Int((farm?.happiness ?? 0.5) * 100))%")
                        .font(.headline)
                        .foregroundColor(happinessColor)
                }
                
                ProgressView(value: farm?.happiness ?? 0.5)
                    .progressViewStyle(LinearProgressViewStyle(tint: happinessColor))
                    .scaleEffect(x: 1, y: 2, anchor: .center)
            }
            .padding()
            .background(Color.gray.opacity(0.1))
            .cornerRadius(12)
        }
    }
    
    private var happinessColor: Color {
        let happiness = farm?.happiness ?? 0.5
        switch happiness {
        case 0.8...1.0: return .green
        case 0.6..<0.8: return .yellow
        case 0.4..<0.6: return .orange
        default: return .red
        }
    }
    
    // MARK: - Statistics
    private var statisticsSection: some View {
        VStack(spacing: 15) {
            Text("Farm Statistics")
                .font(.headline)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                StatCard(
                    title: "Total Buildings",
                    value: "\(farm?.totalBuildings ?? 0)",
                    icon: "🏗️",
                    color: .blue
                )
                
                StatCard(
                    title: "Farm Rating",
                    value: "\(farm?.farmRating ?? 0)",
                    icon: "⭐",
                    color: .yellow
                )
                
                StatCard(
                    title: "Days Active",
                    value: "\(daysActive)",
                    icon: "📅",
                    color: .green
                )
                
                StatCard(
                    title: "Farm Value",
                    value: "🪙\(calculateFarmValue())",
                    icon: "💰",
                    color: .orange
                )
            }
        }
    }
    
    // MARK: - Rhythm Bonus
    private var rhythmBonusSection: some View {
        VStack(spacing: 15) {
            Text("Rhythm Game Integration")
                .font(.headline)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            VStack(spacing: 12) {
                // Current Bonus
                HStack {
                    VStack(alignment: .leading) {
                        Text("Current Rhythm Bonus")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                        
                        Text("\(String(format: "%.1f", rhythmBonus))x")
                            .font(.title2.bold())
                            .foregroundColor(.blue)
                    }
                    
                    Spacer()
                    
                    Text("🎵")
                        .font(.system(size: 40))
                }
                
                Divider()
                
                // Bonus Explanation
                VStack(alignment: .leading, spacing: 8) {
                    Text("How it works:")
                        .font(.subheadline.bold())
                    
                    Text("• Higher farm happiness = better rhythm game scores")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text("• Better rhythm scores = increased farm happiness")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text("• Daily rhythm play = streak bonuses")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                // Streak Info
                if rhythmStreak > 0 {
                    HStack {
                        Text("🔥 Current Streak:")
                        Text("\(rhythmStreak) days")
                            .font(.headline.bold())
                            .foregroundColor(.orange)
                    }
                    .padding(.top, 8)
                }
            }
            .padding()
            .background(Color.blue.opacity(0.1))
            .cornerRadius(12)
        }
    }
    
    // MARK: - Buildings
    private var buildingsSection: some View {
        VStack(spacing: 15) {
            HStack {
                Text("Buildings")
                    .font(.headline)
                
                Spacer()
                
                Text("\(farm?.totalBuildings ?? 0) total")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            if let buildings = farm?.buildings?.allObjects as? [Building], !buildings.isEmpty {
                LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 3), spacing: 10) {
                    ForEach(buildingTypeCounts.keys.sorted(by: { $0.rawValue < $1.rawValue }), id: \.self) { type in
                        if let count = buildingTypeCounts[type], count > 0 {
                            BuildingCountCard(
                                type: type,
                                count: count
                            )
                        }
                    }
                }
            } else {
                Text("No buildings placed yet")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(8)
            }
        }
    }
    
    // MARK: - Animals
    private var animalsSection: some View {
        VStack(spacing: 15) {
            HStack {
                Text("Farm Animals")
                    .font(.headline)
                
                Spacer()
                
                Text("\(activeFarmAnimals.count) placed")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            if !activeFarmAnimals.isEmpty {
                LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 4), spacing: 10) {
                    ForEach(activeFarmAnimals, id: \.objectID) { farmAnimal in
                        if let animal = farmAnimal.animal {
                            AnimalCard(animal: animal)
                        }
                    }
                }
            } else {
                Text("No animals placed on farm yet")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(8)
            }
        }
    }
    
    // MARK: - Daily Rewards
    private var dailyRewardsSection: some View {
        VStack(spacing: 15) {
            Text("Daily Farm Rewards")
                .font(.headline)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            Button(action: collectDailyRewards) {
                HStack {
                    VStack(alignment: .leading) {
                        Text("Collect Daily Rewards")
                            .font(.headline)
                            .foregroundColor(.white)
                        
                        Text("Based on your farm's productivity")
                            .font(.caption)
                            .foregroundColor(.white.opacity(0.8))
                    }
                    
                    Spacer()
                    
                    Text("🎁")
                        .font(.system(size: 30))
                }
                .padding()
                .background(
                    LinearGradient(
                        gradient: Gradient(colors: [.green, .blue]),
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
                .cornerRadius(12)
            }
        }
    }
    
    // MARK: - Computed Properties
    private var daysActive: Int {
        guard let createdAt = farm?.player?.createdAt else { return 0 }
        return Calendar.current.dateComponents([.day], from: createdAt, to: Date()).day ?? 0
    }
    
    private var rhythmBonus: Float {
        let happiness = farm?.happiness ?? 0.5
        return 1.0 + (happiness - 0.5) * 0.5
    }
    
    private var rhythmStreak: Int {
        // This would be calculated from player's rhythm game history
        // For now, return a placeholder
        return 3
    }
    
    private var buildingTypeCounts: [BuildingConfiguration.BuildingType: Int] {
        guard let buildings = farm?.buildings?.allObjects as? [Building] else {
            return [:]
        }
        
        var counts: [BuildingConfiguration.BuildingType: Int] = [:]
        for building in buildings {
            if let type = BuildingConfiguration.BuildingType(rawValue: building.type ?? "") {
                counts[type, default: 0] += 1
            }
        }
        return counts
    }
    
    private var activeFarmAnimals: [FarmAnimal] {
        guard let farmAnimals = farm?.farmAnimals?.allObjects as? [FarmAnimal] else {
            return []
        }
        return farmAnimals.filter { $0.isActive }
    }
    
    // MARK: - Actions
    private func calculateFarmValue() -> Int {
        guard let buildings = farm?.buildings?.allObjects as? [Building] else {
            return 0
        }
        
        let buildingValue = buildings.reduce(0) { total, building in
            let config = BuildingConfiguration.defaultBuildings.first { $0.type.rawValue == building.type }
            let baseValue = config?.baseCost ?? 0
            let upgradeValue = config?.upgradeCosts.prefix(Int(building.level - 1)).reduce(0, +) ?? 0
            return total + baseValue + upgradeValue
        }
        
        let animalValue = activeFarmAnimals.count * 50
        
        return buildingValue + animalValue
    }
    
    private func collectDailyRewards() {
        // This would integrate with FarmViewModel
        let baseCoins = Int32((farm?.totalBuildings ?? 0) * 10)
        let happinessBonus = Int32((farm?.happiness ?? 0.5) * 20)
        let levelBonus = (farm?.level ?? 1) * 5
        
        let rewards = DailyRewards(
            coins: baseCoins + happinessBonus + levelBonus,
            experience: (farm?.level ?? 1) * 2,
            specialItems: []
        )
        
        dailyRewards = rewards
        showDailyRewards = true
    }
}

// MARK: - Supporting Views
struct StatCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 8) {
            Text(icon)
                .font(.system(size: 30))
            
            Text(value)
                .font(.title2.bold())
                .foregroundColor(color)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(10)
    }
}

struct BuildingCountCard: View {
    let type: BuildingConfiguration.BuildingType
    let count: Int
    
    var body: some View {
        VStack(spacing: 4) {
            Text(type.emoji)
                .font(.title2)
            
            Text("\(count)")
                .font(.headline.bold())
                .foregroundColor(.blue)
            
            Text(type.displayName)
                .font(.caption2)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 8)
        .background(Color.blue.opacity(0.1))
        .cornerRadius(8)
    }
}

struct AnimalCard: View {
    let animal: Animal
    
    var body: some View {
        VStack(spacing: 4) {
            Text(animal.emoji)
                .font(.title2)
            
            Text("Lv.\(animal.level)")
                .font(.caption2.bold())
                .foregroundColor(.green)
            
            Text(animal.displayName)
                .font(.caption2)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 8)
        .background(Color.green.opacity(0.1))
        .cornerRadius(8)
    }
}

struct DailyRewardsView: View {
    let rewards: DailyRewards
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack(spacing: 30) {
                Text("🎁")
                    .font(.system(size: 80))
                
                Text("Daily Rewards Collected!")
                    .font(.title.bold())
                
                VStack(spacing: 15) {
                    RewardRow(icon: "🪙", title: "Coins", amount: "\(rewards.coins)")
                    RewardRow(icon: "⭐", title: "Experience", amount: "\(rewards.experience)")
                }
                .padding()
                .background(Color.gray.opacity(0.1))
                .cornerRadius(12)
                
                Button("Awesome!") {
                    dismiss()
                }
                .font(.headline.bold())
                .foregroundColor(.white)
                .frame(width: 200, height: 50)
                .background(Color.green)
                .cornerRadius(25)
                
                Spacer()
            }
            .padding()
            .navigationTitle("Daily Rewards")
            .navigationBarTitleDisplayMode(.inline)
        }
    }
}

struct RewardRow: View {
    let icon: String
    let title: String
    let amount: String
    
    var body: some View {
        HStack {
            Text(icon)
                .font(.title2)
            
            Text(title)
                .font(.headline)
            
            Spacer()
            
            Text("+\(amount)")
                .font(.headline.bold())
                .foregroundColor(.green)
        }
    }
}

// MARK: - Preview
#Preview {
    let sampleFarm = Farm(context: CoreDataStack.preview.viewContext)
    sampleFarm.farmName = "Sample Farm"
    sampleFarm.level = 3
    sampleFarm.happiness = 0.8
    sampleFarm.totalBuildings = 5
    sampleFarm.farmRating = 250
    
    return FarmInfoView(farm: sampleFarm)
        .environmentObject(GameManager.shared)
}
