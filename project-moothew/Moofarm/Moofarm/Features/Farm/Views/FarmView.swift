//
//  FarmView.swift
//  Moofarm
//
//  Created by Project Moothew on 28/7/25.
//

import SwiftUI

/// Main farm view with building placement and management
/// Provides drag & drop interface for farm construction
struct FarmView: View {
    
    // MARK: - Environment
    @EnvironmentObject private var gameManager: GameManager
    @EnvironmentObject private var audioService: AudioService
    @Environment(\.dismiss) private var dismiss
    
    // MARK: - State
    @StateObject private var viewModel = FarmViewModel()
    @StateObject private var gridSystem = FarmGridSystem()
    @State private var showBuildingMenu = false
    @State private var selectedBuildingType: BuildingConfiguration.BuildingType?
    @State private var farmMode: FarmMode = .viewing
    @State private var showFarmInfo = false
    
    // MARK: - Drag State
    @State private var dragOffset = CGSize.zero
    @State private var isDragging = false
    
    // MARK: - Body
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // Background
                backgroundView
                
                // Farm Grid
                farmGridView(geometry)
                
                // Buildings
                buildingsView(geometry)
                
                // Preview Building (when dragging)
                if let buildingType = selectedBuildingType, isDragging {
                    previewBuildingView(buildingType, geometry)
                }
                
                // UI Overlays
                farmUIOverlay
                
                // Building Menu
                if showBuildingMenu {
                    buildingMenuOverlay
                }
            }
        }
        .navigationBarHidden(true)
        .onAppear {
            setupFarm()
        }
        .sheet(isPresented: $showFarmInfo) {
            FarmInfoView(farm: viewModel.currentFarm)
        }
    }
    
    // MARK: - Background
    private var backgroundView: some View {
        LinearGradient(
            gradient: Gradient(colors: [
                Color(red: 0.4, green: 0.8, blue: 0.4),  // Light green
                Color(red: 0.3, green: 0.6, blue: 0.3)   // Darker green
            ]),
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
        .ignoresSafeArea()
    }
    
    // MARK: - Farm Grid
    private func farmGridView(_ geometry: GeometryProxy) -> some View {
        let gridSize = FarmGridSystem.Constants.gridSize
        let farmWidth = gridSystem.farmSize.width * gridSize
        let farmHeight = gridSystem.farmSize.height * gridSize
        
        return VStack(spacing: 0) {
            ForEach(0..<Int(gridSystem.farmSize.height), id: \.self) { row in
                HStack(spacing: 0) {
                    ForEach(0..<Int(gridSystem.farmSize.width), id: \.self) { col in
                        let position = GridPosition(x: col, y: row)
                        
                        Rectangle()
                            .stroke(Color.white.opacity(0.3), lineWidth: 0.5)
                            .fill(getCellColor(for: position))
                            .frame(width: gridSize, height: gridSize)
                            .onTapGesture {
                                handleCellTap(at: position)
                            }
                    }
                }
            }
        }
        .frame(width: farmWidth, height: farmHeight)
        .position(
            x: geometry.size.width / 2,
            y: geometry.size.height / 2
        )
    }
    
    private func getCellColor(for position: GridPosition) -> Color {
        if gridSystem.occupiedCells.contains(position) {
            return Color.brown.opacity(0.3)
        } else if position == gridSystem.selectedPosition {
            return gridSystem.isPreviewValid ? Color.green.opacity(0.3) : Color.red.opacity(0.3)
        } else {
            return Color.green.opacity(0.1)
        }
    }
    
    // MARK: - Buildings View
    private func buildingsView(_ geometry: GeometryProxy) -> some View {
        ForEach(gridSystem.getAllBuildings()) { building in
            BuildingView(
                buildingData: building,
                gridSystem: gridSystem,
                geometry: geometry,
                onTap: {
                    handleBuildingTap(building)
                },
                onLongPress: {
                    handleBuildingLongPress(building)
                }
            )
        }
    }
    
    // MARK: - Preview Building
    private func previewBuildingView(_ buildingType: BuildingConfiguration.BuildingType, _ geometry: GeometryProxy) -> some View {
        let config = BuildingConfiguration.defaultBuildings.first { $0.type == buildingType }!
        let gridSize = FarmGridSystem.Constants.gridSize
        
        return Rectangle()
            .fill(gridSystem.isPreviewValid ? Color.green.opacity(0.5) : Color.red.opacity(0.5))
            .frame(
                width: config.size.width * gridSize,
                height: config.size.height * gridSize
            )
            .overlay(
                Text(config.type.emoji)
                    .font(.system(size: 30))
            )
            .position(
                x: geometry.size.width / 2 + dragOffset.width,
                y: geometry.size.height / 2 + dragOffset.height
            )
    }
    
    // MARK: - UI Overlay
    private var farmUIOverlay: some View {
        VStack {
            // Top Bar
            HStack {
                // Back Button
                Button(action: { dismiss() }) {
                    Image(systemName: "arrow.left")
                        .font(.title2)
                        .foregroundColor(.white)
                        .padding()
                        .background(Color.black.opacity(0.5))
                        .clipShape(Circle())
                }
                
                Spacer()
                
                // Farm Info
                Button(action: { showFarmInfo = true }) {
                    VStack(alignment: .trailing) {
                        Text(viewModel.currentFarm?.farmName ?? "My Farm")
                            .font(.headline)
                            .foregroundColor(.white)
                        
                        HStack {
                            Text("Level \(viewModel.currentFarm?.level ?? 1)")
                                .font(.caption)
                                .foregroundColor(.gray)
                            
                            Text("•")
                                .foregroundColor(.gray)
                            
                            Text("Happiness: \(Int((viewModel.currentFarm?.happiness ?? 0.5) * 100))%")
                                .font(.caption)
                                .foregroundColor(.gray)
                        }
                    }
                    .padding()
                    .background(Color.black.opacity(0.5))
                    .cornerRadius(10)
                }
            }
            .padding(.horizontal)
            .padding(.top, 50)
            
            Spacer()
            
            // Bottom Controls
            HStack(spacing: 20) {
                // Mode Toggle
                Button(action: toggleFarmMode) {
                    HStack {
                        Image(systemName: farmMode == .viewing ? "eye" : "hammer")
                        Text(farmMode == .viewing ? "View" : "Build")
                    }
                    .font(.headline)
                    .foregroundColor(.white)
                    .padding()
                    .background(Color.blue)
                    .cornerRadius(20)
                }
                
                // Building Menu
                if farmMode == .building {
                    Button(action: { showBuildingMenu = true }) {
                        HStack {
                            Image(systemName: "plus")
                            Text("Add Building")
                        }
                        .font(.headline)
                        .foregroundColor(.white)
                        .padding()
                        .background(Color.green)
                        .cornerRadius(20)
                    }
                }
                
                Spacer()
                
                // Resources Display
                HStack(spacing: 15) {
                    ResourceDisplay(
                        icon: "🪙",
                        amount: gameManager.currentPlayer?.coins ?? 0
                    )
                    
                    ResourceDisplay(
                        icon: "💎",
                        amount: gameManager.currentPlayer?.gems ?? 0
                    )
                }
            }
            .padding(.horizontal)
            .padding(.bottom, 30)
        }
    }
    
    // MARK: - Building Menu
    private var buildingMenuOverlay: some View {
        ZStack {
            Color.black.opacity(0.5)
                .ignoresSafeArea()
                .onTapGesture {
                    showBuildingMenu = false
                }
            
            VStack(spacing: 20) {
                Text("Select Building")
                    .font(.title2.bold())
                    .foregroundColor(.white)
                
                LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 3), spacing: 15) {
                    ForEach(BuildingConfiguration.defaultBuildings, id: \.type) { config in
                        BuildingMenuButton(
                            config: config,
                            canAfford: canAffordBuilding(config),
                            onTap: {
                                selectBuilding(config.type)
                            }
                        )
                    }
                }
                .padding()
                
                Button("Cancel") {
                    showBuildingMenu = false
                }
                .font(.headline)
                .foregroundColor(.white)
                .padding()
                .background(Color.red)
                .cornerRadius(20)
            }
            .padding()
            .background(Color.black.opacity(0.8))
            .cornerRadius(20)
            .padding()
        }
    }
    
    // MARK: - Actions
    private func setupFarm() {
        viewModel.loadCurrentFarm()
        
        // Load farm layout into grid system
        if let farm = viewModel.currentFarm,
           let layoutData = farm.layoutData,
           let layout = try? JSONDecoder().decode(FarmLayout.self, from: layoutData) {
            gridSystem.importLayout(layout)
        }
        
        _ = audioService.playMusic("farm_background", loop: true)
    }
    
    private func toggleFarmMode() {
        farmMode = farmMode == .viewing ? .building : .viewing
        _ = audioService.playUISound(.tap)
        
        if farmMode == .viewing {
            selectedBuildingType = nil
            gridSystem.clearPreview()
        }
    }
    
    private func selectBuilding(_ buildingType: BuildingConfiguration.BuildingType) {
        guard canAffordBuilding(getBuildingConfig(buildingType)) else {
            _ = audioService.playUISound(.error)
            return
        }
        
        selectedBuildingType = buildingType
        showBuildingMenu = false
        _ = audioService.playUISound(.success)
    }
    
    private func handleCellTap(at position: GridPosition) {
        guard farmMode == .building else { return }
        
        if let buildingType = selectedBuildingType {
            // Try to place building
            if gridSystem.canPlaceBuilding(buildingType, at: position) {
                placeBuildingAt(position, type: buildingType)
            } else {
                _ = audioService.playUISound(.error)
            }
        } else {
            // Select cell for preview
            gridSystem.selectedPosition = position
        }
    }
    
    private func handleBuildingTap(_ building: PlacedBuildingData) {
        if farmMode == .building {
            // Show building options (upgrade, remove, etc.)
            print("🏗️ Building tapped: \(building.type)")
        } else {
            // Show building info
            print("ℹ️ Building info: \(building.type)")
        }
    }
    
    private func handleBuildingLongPress(_ building: PlacedBuildingData) {
        // Show context menu for building
        print("📱 Building long press: \(building.type)")
    }
    
    private func placeBuildingAt(_ position: GridPosition, type: BuildingConfiguration.BuildingType) {
        guard let config = getBuildingConfig(type),
              let player = gameManager.currentPlayer else { return }
        
        // Check if player can afford
        guard player.canAfford(coins: Int32(config.baseCost)) else {
            _ = audioService.playUISound(.error)
            return
        }
        
        // Place building in grid system
        if gridSystem.placeBuilding(type, at: position) {
            // Deduct cost from player
            player.spend(coins: Int32(config.baseCost))
            
            // Update farm data
            saveFarmLayout()
            
            // Play success sound
            _ = audioService.playUISound(.success)
            
            // Clear selection
            selectedBuildingType = nil
            
            print("🏗️ Placed \(config.name) at \(position)")
        } else {
            _ = audioService.playUISound(.error)
        }
    }
    
    private func saveFarmLayout() {
        guard let farm = viewModel.currentFarm else { return }
        
        let layout = gridSystem.exportLayout()
        if let layoutData = try? JSONEncoder().encode(layout) {
            farm.layoutData = layoutData
            farm.lastUpdated = Date()
            farm.totalBuildings = Int32(gridSystem.getAllBuildings().count)
            
            // Save to Core Data
            viewModel.saveFarm()
        }
    }
    
    private func canAffordBuilding(_ config: BuildingConfiguration?) -> Bool {
        guard let config = config,
              let player = gameManager.currentPlayer else { return false }
        return player.canAfford(coins: Int32(config.baseCost))
    }
    
    private func getBuildingConfig(_ type: BuildingConfiguration.BuildingType) -> BuildingConfiguration? {
        return BuildingConfiguration.defaultBuildings.first { $0.type == type }
    }
}

// MARK: - Supporting Views
struct BuildingView: View {
    let buildingData: PlacedBuildingData
    let gridSystem: FarmGridSystem
    let geometry: GeometryProxy
    let onTap: () -> Void
    let onLongPress: () -> Void
    
    var body: some View {
        let config = BuildingConfiguration.defaultBuildings.first { $0.type == buildingData.type }!
        let position = gridSystem.gridToScreen(buildingData.position, in: geometry)
        let gridSize = FarmGridSystem.Constants.gridSize
        
        Rectangle()
            .fill(Color.brown.opacity(0.8))
            .frame(
                width: config.size.width * gridSize,
                height: config.size.height * gridSize
            )
            .overlay(
                VStack {
                    Text(config.type.emoji)
                        .font(.system(size: 30))
                    
                    if buildingData.level > 1 {
                        Text("Lv.\(buildingData.level)")
                            .font(.caption)
                            .foregroundColor(.white)
                            .padding(2)
                            .background(Color.blue)
                            .cornerRadius(4)
                    }
                }
            )
            .position(position)
            .onTapGesture {
                onTap()
            }
            .onLongPressGesture {
                onLongPress()
            }
    }
}

struct BuildingMenuButton: View {
    let config: BuildingConfiguration
    let canAfford: Bool
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            VStack(spacing: 8) {
                Text(config.type.emoji)
                    .font(.system(size: 40))
                
                Text(config.name)
                    .font(.caption)
                    .foregroundColor(.white)
                
                Text("🪙\(config.baseCost)")
                    .font(.caption2)
                    .foregroundColor(canAfford ? .green : .red)
            }
            .frame(width: 80, height: 80)
            .background(canAfford ? Color.blue.opacity(0.8) : Color.gray.opacity(0.5))
            .cornerRadius(10)
        }
        .disabled(!canAfford)
    }
}

struct ResourceDisplay: View {
    let icon: String
    let amount: Int32
    
    var body: some View {
        HStack(spacing: 4) {
            Text(icon)
                .font(.title3)
            Text("\(amount)")
                .font(.headline)
                .foregroundColor(.white)
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 6)
        .background(Color.black.opacity(0.5))
        .cornerRadius(15)
    }
}

// MARK: - Farm Mode Enum
enum FarmMode {
    case viewing
    case building
    case decorating
    case editing
}

// MARK: - Preview
#Preview {
    FarmView()
        .environmentObject(GameManager.shared)
        .environmentObject(AudioService.shared)
}
