//
//  FarmViewModel.swift
//  Moofarm
//
//  Created by Project Moothew on 28/7/25.
//

import Foundation
import Combine
import CoreData

/// ViewModel for farm management and progression
/// Handles farm state, building management, and rhythm game integration
class FarmViewModel: BaseViewModel {
    
    // MARK: - Published Properties
    @Published var currentFarm: Farm?
    @Published var farmHappiness: Float = 0.5
    @Published var farmLevel: Int32 = 1
    @Published var totalBuildings: Int32 = 0
    @Published var farmRating: Int32 = 0
    @Published var placedAnimals: [FarmAnimal] = []
    
    // MARK: - Farm Statistics
    @Published var farmStats: FarmStatistics = FarmStatistics()
    
    // MARK: - Rhythm Integration
    @Published var rhythmBonus: Float = 1.0
    @Published var lastRhythmScore: Int = 0
    @Published var rhythmStreakDays: Int = 0
    
    // MARK: - Services
    private let gameManager: GameManager
    private let dataService: DataService
    private let audioService: AudioService
    
    // MARK: - Initialization
    init(
        gameManager: GameManager = GameManager.shared,
        dataService: DataService = DataService.shared,
        audioService: AudioService = AudioService.shared
    ) {
        self.gameManager = gameManager
        self.dataService = dataService
        self.audioService = audioService
        
        super.init()
        setupBindings()
    }
    
    // MARK: - Setup
    private func setupBindings() {
        // Update farm stats when farm changes
        $currentFarm
            .compactMap { $0 }
            .sink { [weak self] farm in
                self?.updateFarmStats(from: farm)
            }
            .store(in: &cancellables)
        
        // Calculate rhythm bonus based on farm happiness
        $farmHappiness
            .map { happiness in
                1.0 + (happiness - 0.5) * 0.5 // 0.75x to 1.25x multiplier
            }
            .assign(to: \.rhythmBonus, on: self)
            .store(in: &cancellables)
    }
    
    private func updateFarmStats(from farm: Farm) {
        farmHappiness = farm.happiness
        farmLevel = farm.level
        totalBuildings = farm.totalBuildings
        farmRating = farm.farmRating
        
        // Load placed animals
        if let farmAnimals = farm.farmAnimals?.allObjects as? [FarmAnimal] {
            placedAnimals = farmAnimals.filter { $0.isActive }
        }
        
        // Calculate farm statistics
        calculateFarmStatistics()
    }
    
    // MARK: - Farm Management
    
    /// Load current player's farm
    func loadCurrentFarm() {
        guard let player = gameManager.currentPlayer else {
            handleError(FarmError.noPlayerFound)
            return
        }
        
        if let existingFarm = player.farm {
            currentFarm = existingFarm
        } else {
            // Create new farm for player
            createNewFarm(for: player)
        }
    }
    
    private func createNewFarm(for player: Player) {
        let farm = Farm(context: dataService.viewContext)
        farm.farmName = "\(player.playerName ?? "Player")'s Farm"
        farm.level = 1
        farm.happiness = 0.7
        farm.lastUpdated = Date()
        farm.totalBuildings = 0
        farm.farmRating = 0
        farm.player = player
        
        // Create default layout
        let layout = FarmLayout.empty
        if let layoutData = try? JSONEncoder().encode(layout) {
            farm.layoutData = layoutData
        }
        
        currentFarm = farm
        saveFarm()
        
        print("🏡 Created new farm for player")
    }
    
    /// Save farm changes to Core Data
    func saveFarm() {
        setLoading(true)
        
        dataService.save()
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completion in
                    self?.setLoading(false)
                    if case .failure(let error) = completion {
                        self?.handleError(error)
                    }
                },
                receiveValue: { [weak self] in
                    print("✅ Farm saved successfully")
                    self?.calculateFarmStatistics()
                }
            )
            .store(in: &cancellables)
    }
    
    // MARK: - Building Management
    
    /// Get available buildings for current farm level
    func getAvailableBuildings() -> [BuildingConfiguration] {
        let allBuildings = BuildingConfiguration.defaultBuildings
        
        // Filter buildings based on farm level
        return allBuildings.filter { building in
            switch building.type {
            case .barn, .house:
                return true // Always available
            case .coop:
                return farmLevel >= 2
            case .pond:
                return farmLevel >= 3
            case .musicHall:
                return farmLevel >= 5
            case .practiceRoom:
                return farmLevel >= 4
            case .decoration:
                return farmLevel >= 2
            }
        }
    }
    
    /// Calculate building efficiency based on placement and farm happiness
    func getBuildingEfficiency(for buildingType: BuildingConfiguration.BuildingType) -> Float {
        guard let config = BuildingConfiguration.defaultBuildings.first(where: { $0.type == buildingType }) else {
            return 1.0
        }
        
        var efficiency = config.productionRate
        
        // Apply farm happiness bonus
        efficiency *= (0.5 + farmHappiness * 0.5) // 0.5x to 1.0x based on happiness
        
        // Apply rhythm bonus
        efficiency *= rhythmBonus
        
        return efficiency
    }
    
    /// Upgrade building at position
    func upgradeBuilding(at position: GridPosition) -> AnyPublisher<Bool, Error> {
        guard let farm = currentFarm else {
            return Fail(error: FarmError.noFarmFound).eraseToAnyPublisher()
        }
        
        return Future { [weak self] promise in
            // Find building in Core Data
            let request = Building.fetchRequest()
            request.predicate = NSPredicate(format: "farm == %@ AND positionX == %d AND positionY == %d", 
                                          farm, position.x, position.y)
            
            do {
                let buildings = try self?.dataService.viewContext.fetch(request) ?? []
                guard let building = buildings.first else {
                    promise(.failure(FarmError.buildingNotFound))
                    return
                }
                
                // Check if can upgrade
                let config = BuildingConfiguration.defaultBuildings.first { $0.type.rawValue == building.type }
                guard let buildingConfig = config,
                      building.level < buildingConfig.maxLevel else {
                    promise(.failure(FarmError.maxLevelReached))
                    return
                }
                
                // Check cost
                let upgradeCost = buildingConfig.upgradeCosts[Int(building.level - 1)]
                guard let player = self?.gameManager.currentPlayer,
                      player.canAfford(coins: Int32(upgradeCost)) else {
                    promise(.failure(FarmError.insufficientFunds))
                    return
                }
                
                // Perform upgrade
                player.spend(coins: Int32(upgradeCost))
                building.level += 1
                building.lastUpgradedAt = Date()
                building.productionRate = buildingConfig.productionRate * Float(building.level)
                
                // Save changes
                try self?.dataService.viewContext.save()
                promise(.success(true))
                
            } catch {
                promise(.failure(error))
            }
        }
        .eraseToAnyPublisher()
    }
    
    // MARK: - Animal Management
    
    /// Place animal on farm
    func placeAnimal(_ animal: Animal, at position: GridPosition) -> AnyPublisher<Bool, Error> {
        guard let farm = currentFarm else {
            return Fail(error: FarmError.noFarmFound).eraseToAnyPublisher()
        }
        
        return Future { [weak self] promise in
            // Check if animal is unlocked
            guard animal.isUnlocked else {
                promise(.failure(FarmError.animalNotUnlocked))
                return
            }
            
            // Create farm animal placement
            let farmAnimal = FarmAnimal(context: self?.dataService.viewContext ?? CoreDataStack.shared.viewContext)
            farmAnimal.animal = animal
            farmAnimal.farm = farm
            farmAnimal.positionX = Int32(position.x)
            farmAnimal.positionY = Int32(position.y)
            farmAnimal.placedAt = Date()
            farmAnimal.isActive = true
            
            // Update farm happiness
            farm.happiness = min(1.0, farm.happiness + 0.05) // Small happiness boost
            farm.lastUpdated = Date()
            
            do {
                try self?.dataService.viewContext.save()
                self?.placedAnimals.append(farmAnimal)
                promise(.success(true))
            } catch {
                promise(.failure(error))
            }
        }
        .eraseToAnyPublisher()
    }
    
    /// Remove animal from farm
    func removeAnimal(at position: GridPosition) -> AnyPublisher<Bool, Error> {
        return Future { [weak self] promise in
            guard let farmAnimal = self?.placedAnimals.first(where: { 
                $0.positionX == position.x && $0.positionY == position.y 
            }) else {
                promise(.failure(FarmError.animalNotFound))
                return
            }
            
            farmAnimal.isActive = false
            
            do {
                try self?.dataService.viewContext.save()
                self?.placedAnimals.removeAll { $0 == farmAnimal }
                promise(.success(true))
            } catch {
                promise(.failure(error))
            }
        }
        .eraseToAnyPublisher()
    }
    
    // MARK: - Rhythm Game Integration
    
    /// Process rhythm game results and apply farm bonuses
    func processRhythmGameResults(_ scoreData: ScoreData) {
        guard let farm = currentFarm else { return }
        
        lastRhythmScore = scoreData.totalScore
        
        // Calculate happiness bonus based on performance
        let performanceRatio = scoreData.accuracyPercentage / 100.0
        let happinessBonus = Float(performanceRatio * 0.1) // Up to 10% happiness boost
        
        // Apply happiness bonus
        farm.happiness = min(1.0, farm.happiness + happinessBonus)
        
        // Update rhythm streak
        updateRhythmStreak()
        
        // Apply streak bonuses
        if rhythmStreakDays >= 3 {
            // Bonus coins for streak
            let streakBonus = Int32(rhythmStreakDays * 10)
            gameManager.currentPlayer?.add(coins: streakBonus)
            print("🔥 Rhythm streak bonus: +\(streakBonus) coins")
        }
        
        // Check for farm level up
        checkFarmLevelUp()
        
        saveFarm()
        
        print("🎵 Processed rhythm results - Happiness: \(farm.happiness), Streak: \(rhythmStreakDays)")
    }
    
    private func updateRhythmStreak() {
        let today = Calendar.current.startOfDay(for: Date())
        let lastPlayDate = gameManager.currentPlayer?.lastPlayedAt ?? Date()
        let lastPlayDay = Calendar.current.startOfDay(for: lastPlayDate)
        
        if Calendar.current.dateComponents([.day], from: lastPlayDay, to: today).day == 1 {
            // Consecutive day
            rhythmStreakDays += 1
        } else if Calendar.current.dateComponents([.day], from: lastPlayDay, to: today).day ?? 0 > 1 {
            // Streak broken
            rhythmStreakDays = 1
        }
        // Same day = no change
    }
    
    private func checkFarmLevelUp() {
        guard let farm = currentFarm else { return }
        
        let requiredBuildings = Int32(farm.level * 3) // 3 buildings per level
        let requiredHappiness: Float = 0.6 + (Float(farm.level) * 0.1) // Increasing happiness requirement
        
        if farm.totalBuildings >= requiredBuildings && farm.happiness >= requiredHappiness {
            farm.level += 1
            farm.farmRating += 100 // Rating boost for level up
            
            // Unlock new buildings
            print("🎉 Farm leveled up to level \(farm.level)!")
            
            // Play level up sound
            _ = audioService.playUISound(.levelUp)
        }
    }
    
    // MARK: - Statistics
    
    private func calculateFarmStatistics() {
        guard let farm = currentFarm else { return }
        
        let buildings = farm.buildings?.allObjects as? [Building] ?? []
        let animals = placedAnimals
        
        farmStats = FarmStatistics(
            totalBuildings: buildings.count,
            totalAnimals: animals.count,
            averageBuildingLevel: buildings.isEmpty ? 0 : buildings.map { Float($0.level) }.reduce(0, +) / Float(buildings.count),
            totalProductionRate: buildings.map { $0.productionRate }.reduce(0, +),
            farmValue: calculateFarmValue(buildings: buildings, animals: animals),
            daysActive: Calendar.current.dateComponents([.day], from: farm.player?.createdAt ?? Date(), to: Date()).day ?? 0,
            lastRhythmBonus: rhythmBonus,
            rhythmStreak: rhythmStreakDays
        )
    }
    
    private func calculateFarmValue(buildings: [Building], animals: [FarmAnimal]) -> Int {
        let buildingValue = buildings.reduce(0) { total, building in
            let config = BuildingConfiguration.defaultBuildings.first { $0.type.rawValue == building.type }
            let baseValue = config?.baseCost ?? 0
            let upgradeValue = config?.upgradeCosts.prefix(Int(building.level - 1)).reduce(0, +) ?? 0
            return total + baseValue + upgradeValue
        }
        
        let animalValue = animals.count * 50 // Base value per animal
        
        return buildingValue + animalValue
    }
    
    /// Get farm productivity bonus for rhythm game
    func getRhythmGameBonus() -> Float {
        return rhythmBonus
    }
    
    /// Get daily farm rewards
    func collectDailyRewards() -> AnyPublisher<DailyRewards, Error> {
        return Future { [weak self] promise in
            guard let self = self,
                  let farm = self.currentFarm,
                  let player = self.gameManager.currentPlayer else {
                promise(.failure(FarmError.noFarmFound))
                return
            }
            
            // Calculate daily rewards based on farm productivity
            let baseCoins = Int32(farm.totalBuildings * 10)
            let happinessBonus = Int32(farm.happiness * 20)
            let levelBonus = farm.level * 5
            
            let totalCoins = baseCoins + happinessBonus + levelBonus
            let experience = Int32(farm.level * 2)
            
            // Apply rewards
            player.add(coins: totalCoins, experience: experience)
            
            let rewards = DailyRewards(
                coins: totalCoins,
                experience: experience,
                specialItems: []
            )
            
            promise(.success(rewards))
        }
        .eraseToAnyPublisher()
    }
}

// MARK: - Supporting Models
struct FarmStatistics {
    var totalBuildings: Int = 0
    var totalAnimals: Int = 0
    var averageBuildingLevel: Float = 0
    var totalProductionRate: Float = 0
    var farmValue: Int = 0
    var daysActive: Int = 0
    var lastRhythmBonus: Float = 1.0
    var rhythmStreak: Int = 0
}

struct DailyRewards {
    let coins: Int32
    let experience: Int32
    let specialItems: [String]
}

// MARK: - Error Types
enum FarmError: LocalizedError {
    case noPlayerFound
    case noFarmFound
    case buildingNotFound
    case animalNotFound
    case animalNotUnlocked
    case maxLevelReached
    case insufficientFunds
    case invalidPosition
    
    var errorDescription: String? {
        switch self {
        case .noPlayerFound:
            return "No player found"
        case .noFarmFound:
            return "No farm found"
        case .buildingNotFound:
            return "Building not found"
        case .animalNotFound:
            return "Animal not found"
        case .animalNotUnlocked:
            return "Animal not unlocked"
        case .maxLevelReached:
            return "Maximum level reached"
        case .insufficientFunds:
            return "Insufficient funds"
        case .invalidPosition:
            return "Invalid position"
        }
    }
}
