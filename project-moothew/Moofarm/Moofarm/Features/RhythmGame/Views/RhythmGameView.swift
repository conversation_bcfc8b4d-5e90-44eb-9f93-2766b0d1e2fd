//
//  RhythmGameView.swift
//  Moofarm
//
//  Created by Project Moothew on 28/7/25.
//

import SwiftUI
import SpriteKit

/// SwiftUI container for the rhythm game
/// Handles the interface between SwiftUI and SpriteKit
struct RhythmGameView: View {
    
    // MARK: - Environment
    @EnvironmentObject private var audioService: AudioService
    @EnvironmentObject private var gameManager: GameManager
    @Environment(\.dismiss) private var dismiss
    
    // MARK: - State
    @StateObject private var viewModel = RhythmGameViewModel()
    @State private var scene: RhythmGameScene?
    @State private var showPauseMenu = false
    @State private var showResultsScreen = false
    @State private var gameResults: ScoreData?
    
    // MARK: - Properties
    let song: Song
    
    // MARK: - Initialization
    init(song: Song) {
        self.song = song
    }
    
    // MARK: - Body
    var body: some View {
        ZStack {
            // SpriteKit Scene
            if let scene = scene {
                SpriteView(scene: scene)
                    .ignoresSafeArea()
                    .onAppear {
                        startGame()
                    }
            } else {
                // Loading state
                loadingView
            }
            
            // Pause Menu Overlay
            if showPauseMenu {
                pauseMenuOverlay
            }
            
            // Game Controls
            gameControlsOverlay
        }
        .navigationBarHidden(true)
        .onAppear {
            setupScene()
        }
        .onDisappear {
            cleanup()
        }
        .sheet(isPresented: $showResultsScreen) {
            if let results = gameResults {
                RhythmGameResultsView(
                    song: song,
                    scoreData: results,
                    onPlayAgain: {
                        restartGame()
                    },
                    onBackToMenu: {
                        dismiss()
                    }
                )
            }
        }
    }
    
    // MARK: - Loading View
    private var loadingView: some View {
        VStack(spacing: 20) {
            Text("🎵")
                .font(.system(size: 80))
            
            Text("Loading \(song.title ?? "Song")...")
                .font(.title2)
                .foregroundColor(.white)
            
            ProgressView()
                .scaleEffect(1.5)
                .tint(.white)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color.black)
    }
    
    // MARK: - Pause Menu
    private var pauseMenuOverlay: some View {
        ZStack {
            Color.black.opacity(0.8)
                .ignoresSafeArea()
            
            VStack(spacing: 30) {
                Text("Game Paused")
                    .font(.largeTitle.bold())
                    .foregroundColor(.white)
                
                VStack(spacing: 20) {
                    Button("Resume") {
                        resumeGame()
                    }
                    .buttonStyle(PauseMenuButtonStyle(color: .green))
                    
                    Button("Restart") {
                        restartGame()
                    }
                    .buttonStyle(PauseMenuButtonStyle(color: .blue))
                    
                    Button("Back to Menu") {
                        dismiss()
                    }
                    .buttonStyle(PauseMenuButtonStyle(color: .red))
                }
            }
        }
    }
    
    // MARK: - Game Controls
    private var gameControlsOverlay: some View {
        VStack {
            HStack {
                // Pause button
                Button(action: pauseGame) {
                    Image(systemName: "pause.fill")
                        .font(.title2)
                        .foregroundColor(.white)
                        .padding()
                        .background(Color.black.opacity(0.5))
                        .clipShape(Circle())
                }
                .padding(.leading)
                
                Spacer()
                
                // Song info
                VStack(alignment: .trailing) {
                    Text(song.title ?? "Unknown Song")
                        .font(.headline)
                        .foregroundColor(.white)
                    
                    Text(song.difficulty?.capitalized ?? "Normal")
                        .font(.caption)
                        .foregroundColor(.gray)
                }
                .padding(.trailing)
            }
            .padding(.top, 50)
            
            Spacer()
        }
    }
    
    // MARK: - Scene Management
    private func setupScene() {
        let newScene = RhythmGameScene()
        newScene.size = CGSize(width: UIScreen.main.bounds.width, height: UIScreen.main.bounds.height)
        newScene.scaleMode = .aspectFill
        newScene.gameDelegate = viewModel
        
        self.scene = newScene
    }
    
    private func startGame() {
        guard let scene = scene else { return }
        
        scene.startGame(with: song, audioService: audioService)
        viewModel.gameState = .playing
    }
    
    private func pauseGame() {
        guard let scene = scene, viewModel.gameState == .playing else { return }
        
        scene.pauseGame()
        showPauseMenu = true
        viewModel.gameState = .paused
    }
    
    private func resumeGame() {
        guard let scene = scene, viewModel.gameState == .paused else { return }
        
        scene.resumeGame()
        showPauseMenu = false
        viewModel.gameState = .playing
    }
    
    private func restartGame() {
        showPauseMenu = false
        showResultsScreen = false
        gameResults = nil
        
        // Reset scene
        setupScene()
        
        // Start new game
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            startGame()
        }
    }
    
    private func cleanup() {
        scene?.removeFromParent()
        audioService.stopMusic()
        audioService.stopBeatDetection()
    }
}

// MARK: - Pause Menu Button Style
struct PauseMenuButtonStyle: ButtonStyle {
    let color: Color
    
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .font(.title2.bold())
            .foregroundColor(.white)
            .frame(width: 200, height: 50)
            .background(color)
            .cornerRadius(25)
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}

// MARK: - Results View
struct RhythmGameResultsView: View {
    let song: Song
    let scoreData: ScoreData
    let onPlayAgain: () -> Void
    let onBackToMenu: () -> Void
    
    var body: some View {
        NavigationView {
            VStack(spacing: 30) {
                // Header
                VStack(spacing: 10) {
                    Text("🎉")
                        .font(.system(size: 80))
                    
                    Text("Song Complete!")
                        .font(.largeTitle.bold())
                    
                    Text(song.title ?? "Unknown Song")
                        .font(.title2)
                        .foregroundColor(.secondary)
                }
                
                // Score Display
                VStack(spacing: 20) {
                    ScoreCard(title: "Final Score", value: "\(scoreData.totalScore)", color: .blue)
                    
                    HStack(spacing: 20) {
                        ScoreCard(title: "Max Combo", value: "\(scoreData.combo)", color: .orange)
                        ScoreCard(title: "Accuracy", value: "\(Int(scoreData.accuracyPercentage))%", color: .green)
                    }
                    
                    HStack(spacing: 15) {
                        HitStatCard(title: "Perfect", count: scoreData.perfectHits, color: .yellow)
                        HitStatCard(title: "Good", count: scoreData.goodHits, color: .green)
                        HitStatCard(title: "Okay", count: scoreData.okayHits, color: .blue)
                        HitStatCard(title: "Miss", count: scoreData.missedHits, color: .red)
                    }
                }
                
                // Rewards
                VStack(spacing: 10) {
                    Text("Rewards Earned")
                        .font(.headline)
                    
                    HStack(spacing: 30) {
                        RewardCard(icon: "🪙", title: "Coins", amount: scoreData.coinsEarned)
                        RewardCard(icon: "⭐", title: "XP", amount: scoreData.experienceEarned)
                    }
                }
                
                // Grade
                VStack(spacing: 5) {
                    Text("Grade")
                        .font(.headline)
                    
                    Text(scoreData.grade)
                        .font(.system(size: 60, weight: .bold))
                        .foregroundColor(gradeColor(scoreData.grade))
                }
                
                Spacer()
                
                // Action Buttons
                HStack(spacing: 20) {
                    Button("Play Again") {
                        onPlayAgain()
                    }
                    .buttonStyle(ResultsButtonStyle(color: .blue))
                    
                    Button("Back to Menu") {
                        onBackToMenu()
                    }
                    .buttonStyle(ResultsButtonStyle(color: .gray))
                }
            }
            .padding()
            .navigationTitle("Results")
            .navigationBarTitleDisplayMode(.inline)
        }
    }
    
    private func gradeColor(_ grade: String) -> Color {
        switch grade {
        case "S": return .yellow
        case "A": return .green
        case "B": return .blue
        case "C": return .orange
        case "D": return .red
        default: return .gray
        }
    }
}

// MARK: - Result Components
struct ScoreCard: View {
    let title: String
    let value: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 5) {
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
            
            Text(value)
                .font(.title.bold())
                .foregroundColor(color)
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(10)
    }
}

struct HitStatCard: View {
    let title: String
    let count: Int
    let color: Color
    
    var body: some View {
        VStack(spacing: 5) {
            Text(title)
                .font(.caption2)
                .foregroundColor(.secondary)
            
            Text("\(count)")
                .font(.headline.bold())
                .foregroundColor(color)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 8)
        .background(Color.gray.opacity(0.1))
        .cornerRadius(8)
    }
}

struct RewardCard: View {
    let icon: String
    let title: String
    let amount: Int
    
    var body: some View {
        VStack(spacing: 5) {
            Text(icon)
                .font(.title)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
            
            Text("+\(amount)")
                .font(.headline.bold())
                .foregroundColor(.primary)
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(Color.green.opacity(0.1))
        .cornerRadius(10)
    }
}

struct ResultsButtonStyle: ButtonStyle {
    let color: Color
    
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .font(.headline.bold())
            .foregroundColor(.white)
            .frame(width: 140, height: 44)
            .background(color)
            .cornerRadius(22)
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}

// MARK: - Preview
#Preview {
    let sampleSong = Song(context: CoreDataStack.preview.viewContext)
    sampleSong.title = "Sample Song"
    sampleSong.difficulty = "normal"
    sampleSong.bpm = 120
    sampleSong.duration = 60
    
    return RhythmGameView(song: sampleSong)
        .environmentObject(AudioService.shared)
        .environmentObject(GameManager.shared)
}
