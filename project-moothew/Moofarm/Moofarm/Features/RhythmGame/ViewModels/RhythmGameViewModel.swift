//
//  RhythmGameViewModel.swift
//  Moofarm
//
//  Created by Project Moothew on 28/7/25.
//

import Foundation
import Combine

/// ViewModel for rhythm game management
/// Handles game state, scoring, and integration with other systems
class RhythmGameViewModel: BaseViewModel, ObservableObject {
    
    // MARK: - Published Properties
    @Published var gameState: RhythmGameState = .idle
    @Published var currentSong: Song?
    @Published var currentScore: Int = 0
    @Published var currentCombo: Int = 0
    @Published var gameProgress: Double = 0.0
    @Published var isGameActive: Bool = false
    
    // MARK: - Game Statistics
    @Published var sessionStats: SessionStats = SessionStats()
    
    // MARK: - Services
    private let gameManager: GameManager
    private let audioService: AudioService
    private let dataService: DataService
    
    // MARK: - Game Data
    private var gameStartTime: Date?
    private var sessionResults: [ScoreData] = []
    
    // MARK: - Initialization
    init(
        gameManager: GameManager = GameManager.shared,
        audioService: AudioService = AudioService.shared,
        dataService: DataService = DataService.shared
    ) {
        self.gameManager = gameManager
        self.audioService = audioService
        self.dataService = dataService
        
        super.init()
        setupBindings()
    }
    
    // MARK: - Setup
    private func setupBindings() {
        // Listen for game state changes
        $gameState
            .sink { [weak self] state in
                self?.handleGameStateChange(state)
            }
            .store(in: &cancellables)
        
        // Update game active status
        $gameState
            .map { $0 == .playing }
            .assign(to: \.isGameActive, on: self)
            .store(in: &cancellables)
    }
    
    private func handleGameStateChange(_ state: RhythmGameState) {
        switch state {
        case .idle:
            resetSession()
        case .playing:
            if gameStartTime == nil {
                gameStartTime = Date()
            }
        case .paused:
            // Pause handling
            break
        case .completed, .failed:
            endSession()
        }
    }
    
    // MARK: - Session Management
    private func resetSession() {
        currentScore = 0
        currentCombo = 0
        gameProgress = 0.0
        gameStartTime = nil
        sessionStats = SessionStats()
        sessionResults.removeAll()
    }
    
    private func endSession() {
        guard let startTime = gameStartTime else { return }
        
        let sessionDuration = Date().timeIntervalSince(startTime)
        sessionStats.totalPlayTime += sessionDuration
        sessionStats.gamesPlayed += 1
        
        // Calculate session averages
        if !sessionResults.isEmpty {
            sessionStats.averageScore = sessionResults.map { $0.totalScore }.reduce(0, +) / sessionResults.count
            sessionStats.averageAccuracy = sessionResults.map { $0.accuracy }.reduce(0, +) / Double(sessionResults.count)
            sessionStats.bestCombo = sessionResults.map { $0.combo }.max() ?? 0
        }
    }
    
    // MARK: - Song Management
    func loadSong(_ song: Song) {
        currentSong = song
        gameState = .idle
        
        // Preload audio
        if let fileName = song.fileName?.replacingOccurrences(of: ".mp3", with: "") {
            // Preload the song (implementation depends on AudioService)
            print("🎵 Loading song: \(song.title ?? "Unknown")")
        }
    }
    
    func getAvailableSongs() -> AnyPublisher<[Song], Error> {
        let request = Song.fetchRequest()
        request.predicate = NSPredicate(format: "isUnlocked == YES")
        request.sortDescriptors = [
            NSSortDescriptor(key: "difficulty", ascending: true),
            NSSortDescriptor(key: "title", ascending: true)
        ]
        
        return dataService.fetch(request)
    }
    
    func getSongsForDifficulty(_ difficulty: Difficulty) -> AnyPublisher<[Song], Error> {
        let request = Song.fetchRequest()
        request.predicate = NSPredicate(format: "isUnlocked == YES AND difficulty == %@", difficulty.rawValue)
        request.sortDescriptors = [NSSortDescriptor(key: "title", ascending: true)]
        
        return dataService.fetch(request)
    }
    
    // MARK: - Score Processing
    func processGameResult(_ scoreData: ScoreData) {
        sessionResults.append(scoreData)
        
        // Update session stats
        sessionStats.totalScore += scoreData.totalScore
        sessionStats.perfectHits += scoreData.perfectHits
        sessionStats.goodHits += scoreData.goodHits
        sessionStats.okayHits += scoreData.okayHits
        sessionStats.missedHits += scoreData.missedHits
        
        if scoreData.combo > sessionStats.bestCombo {
            sessionStats.bestCombo = scoreData.combo
        }
        
        // Process with GameManager
        if let song = currentSong {
            Task {
                await gameManager.handleRhythmGameComplete(scoreData: scoreData, song: song)
            }
        }
        
        // Check for achievements
        checkAchievements(scoreData)
        
        // Update game state
        gameState = .completed
    }
    
    private func checkAchievements(_ scoreData: ScoreData) {
        // Check for rhythm-specific achievements
        if scoreData.combo >= 50 {
            // Perfect combo achievement
            print("🏆 Achievement unlocked: Perfect Combo!")
        }
        
        if scoreData.accuracyPercentage >= 95.0 {
            // High accuracy achievement
            print("🏆 Achievement unlocked: Rhythm Master!")
        }
        
        if scoreData.totalScore >= 100000 {
            // High score achievement
            print("🏆 Achievement unlocked: Score Master!")
        }
        
        // Check session achievements
        if sessionStats.gamesPlayed >= 5 {
            print("🏆 Achievement unlocked: Dedicated Player!")
        }
    }
    
    // MARK: - Statistics
    func getPlayerRhythmStats() -> AnyPublisher<RhythmPlayerStats, Error> {
        guard let player = gameManager.currentPlayer else {
            return Fail(error: DataServiceError.contextNotAvailable)
                .eraseToAnyPublisher()
        }
        
        return Future { promise in
            let progressArray = player.songProgress?.allObjects as? [SongProgress] ?? []
            
            let stats = RhythmPlayerStats(
                totalSongsPlayed: progressArray.count,
                totalSongsCompleted: progressArray.filter { $0.isCompleted }.count,
                averageScore: progressArray.isEmpty ? 0 : progressArray.map { Double($0.highScore) }.reduce(0, +) / Double(progressArray.count),
                bestCombo: progressArray.map { Int($0.bestCombo) }.max() ?? 0,
                totalPerfectHits: progressArray.map { Int($0.perfectHits) }.reduce(0, +),
                totalPlayTime: TimeInterval(player.totalPlayTime),
                favoriteAnimalType: self.getFavoriteAnimalType(from: progressArray)
            )
            
            promise(.success(stats))
        }
        .eraseToAnyPublisher()
    }
    
    private func getFavoriteAnimalType(from progress: [SongProgress]) -> AnimalType {
        // Analyze which animal type appears most in completed songs
        let animalCounts = progress.compactMap { $0.song?.sourceAnimal?.type }
            .reduce(into: [String: Int]()) { counts, type in
                counts[type, default: 0] += 1
            }
        
        let mostCommonType = animalCounts.max { $0.value < $1.value }?.key ?? "cow"
        return AnimalType(rawValue: mostCommonType) ?? .cow
    }
    
    // MARK: - Difficulty Recommendations
    func getRecommendedDifficulty() -> AnyPublisher<Difficulty, Error> {
        return getPlayerRhythmStats()
            .map { stats in
                if stats.averageScore >= 80000 && stats.totalSongsCompleted >= 10 {
                    return .hard
                } else if stats.averageScore >= 50000 && stats.totalSongsCompleted >= 5 {
                    return .normal
                } else {
                    return .easy
                }
            }
            .eraseToAnyPublisher()
    }
    
    func getNextSongRecommendation() -> AnyPublisher<Song?, Error> {
        return getRecommendedDifficulty()
            .flatMap { [weak self] difficulty in
                self?.getSongsForDifficulty(difficulty) ?? Empty().eraseToAnyPublisher()
            }
            .map { songs in
                // Return a random song from recommended difficulty
                songs.randomElement()
            }
            .eraseToAnyPublisher()
    }
    
    // MARK: - Practice Mode
    func startPracticeMode(song: Song, startTime: TimeInterval = 0, endTime: TimeInterval? = nil) {
        currentSong = song
        
        // Configure practice parameters
        // This would be used by the scene to start at specific time
        print("🎯 Starting practice mode for: \(song.title ?? "Unknown")")
        print("   Start time: \(startTime)s")
        if let end = endTime {
            print("   End time: \(end)s")
        }
        
        gameState = .playing
    }
    
    // MARK: - Leaderboard Integration
    func submitScoreToLeaderboard(_ scoreData: ScoreData) {
        // This would integrate with GameKitService
        Task {
            do {
                let gameKitService = GameKitService.shared
                if gameKitService.isAuthenticated {
                    _ = try await gameKitService.submitRhythmScore(scoreData.totalScore).value
                    print("✅ Score submitted to leaderboard: \(scoreData.totalScore)")
                }
            } catch {
                print("❌ Failed to submit score: \(error)")
            }
        }
    }
}

// MARK: - RhythmGameSceneDelegate
extension RhythmGameViewModel: RhythmGameSceneDelegate {
    
    func gameDidStart() {
        gameState = .playing
        print("🎮 Rhythm game started")
    }
    
    func gameDidPause() {
        gameState = .paused
        print("⏸️ Rhythm game paused")
    }
    
    func gameDidResume() {
        gameState = .playing
        print("▶️ Rhythm game resumed")
    }
    
    func gameDidEnd(with scoreData: ScoreData) {
        processGameResult(scoreData)
        submitScoreToLeaderboard(scoreData)
        print("🏁 Rhythm game ended - Score: \(scoreData.totalScore)")
    }
}

// MARK: - Supporting Models
struct SessionStats {
    var gamesPlayed: Int = 0
    var totalScore: Int = 0
    var averageScore: Int = 0
    var bestCombo: Int = 0
    var perfectHits: Int = 0
    var goodHits: Int = 0
    var okayHits: Int = 0
    var missedHits: Int = 0
    var totalPlayTime: TimeInterval = 0
    var averageAccuracy: Double = 0.0
    
    var totalHits: Int {
        return perfectHits + goodHits + okayHits + missedHits
    }
    
    var successRate: Double {
        guard totalHits > 0 else { return 0.0 }
        let successfulHits = perfectHits + goodHits + okayHits
        return Double(successfulHits) / Double(totalHits) * 100.0
    }
}

struct RhythmPlayerStats {
    let totalSongsPlayed: Int
    let totalSongsCompleted: Int
    let averageScore: Double
    let bestCombo: Int
    let totalPerfectHits: Int
    let totalPlayTime: TimeInterval
    let favoriteAnimalType: AnimalType
    
    var completionRate: Double {
        guard totalSongsPlayed > 0 else { return 0.0 }
        return Double(totalSongsCompleted) / Double(totalSongsPlayed) * 100.0
    }
    
    var averagePlayTimePerSong: TimeInterval {
        guard totalSongsPlayed > 0 else { return 0.0 }
        return totalPlayTime / Double(totalSongsPlayed)
    }
}
