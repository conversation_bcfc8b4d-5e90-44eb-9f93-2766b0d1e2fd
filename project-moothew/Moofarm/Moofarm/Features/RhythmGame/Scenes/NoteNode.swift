//
//  NoteNode.swift
//  Moofarm
//
//  Created by Project Moothew on 28/7/25.
//

import SpriteKit

/// Individual note node for rhythm game
/// Represents a single note that players need to tap
class NoteNode: SKSpriteNode {
    
    // MARK: - Properties
    let noteData: NoteData
    var isHit: Bool = false
    
    private var animalSprite: SKSpriteNode!
    private var glowEffect: SKSpriteNode!
    private var trailEffect: SKEmitterNode?
    
    // MARK: - Constants
    private struct Constants {
        static let noteSize = CGSize(width: 60, height: 60)
        static let glowSize = CGSize(width: 80, height: 80)
        static let animationDuration: TimeInterval = 0.2
    }
    
    // MARK: - Initialization
    init(noteData: NoteData) {
        self.noteData = noteData
        
        super.init(texture: nil, color: .clear, size: Constants.noteSize)
        
        setupVisuals()
        setupAnimations()
    }
    
    required init?(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Setup
    private func setupVisuals() {
        // Create base note background
        let noteBackground = SKSpriteNode(color: getNoteColor(), size: Constants.noteSize)
        noteBackground.cornerRadius = Constants.noteSize.width / 2
        addChild(noteBackground)
        
        // Create glow effect
        glowEffect = SKSpriteNode(color: getNoteColor().withAlphaComponent(0.3), size: Constants.glowSize)
        glowEffect.cornerRadius = Constants.glowSize.width / 2
        glowEffect.zPosition = -1
        addChild(glowEffect)
        
        // Create animal sprite
        animalSprite = SKSpriteNode(texture: getAnimalTexture())
        animalSprite.size = CGSize(width: 40, height: 40)
        animalSprite.zPosition = 1
        addChild(animalSprite)
        
        // Add border
        let border = SKShapeNode(circleOfRadius: Constants.noteSize.width / 2)
        border.strokeColor = .white
        border.lineWidth = 2
        border.fillColor = .clear
        border.zPosition = 2
        addChild(border)
        
        // Setup trail effect for special notes
        if noteData.type == .special {
            setupTrailEffect()
        }
    }
    
    private func setupAnimations() {
        // Gentle pulsing animation
        let pulseUp = SKAction.scale(to: 1.1, duration: 0.5)
        let pulseDown = SKAction.scale(to: 1.0, duration: 0.5)
        let pulseSequence = SKAction.sequence([pulseUp, pulseDown])
        let repeatPulse = SKAction.repeatForever(pulseSequence)
        
        glowEffect.run(repeatPulse)
        
        // Rotation animation for special notes
        if noteData.type == .special {
            let rotate = SKAction.rotate(byAngle: CGFloat.pi * 2, duration: 2.0)
            let repeatRotate = SKAction.repeatForever(rotate)
            animalSprite.run(repeatRotate)
        }
        
        // Entry animation
        let initialScale = SKAction.scale(to: 0.1, duration: 0)
        let scaleUp = SKAction.scale(to: 1.0, duration: Constants.animationDuration)
        scaleUp.timingMode = .easeOut
        
        run(SKAction.sequence([initialScale, scaleUp]))
    }
    
    private func setupTrailEffect() {
        // Create simple particle trail for special notes
        trailEffect = SKEmitterNode()
        trailEffect?.particleTexture = SKTexture(imageNamed: "spark") // You'll need to add this asset
        trailEffect?.particleBirthRate = 20
        trailEffect?.particleLifetime = 0.5
        trailEffect?.particleScale = 0.1
        trailEffect?.particleScaleRange = 0.05
        trailEffect?.particleAlpha = 0.8
        trailEffect?.particleAlphaRange = 0.2
        trailEffect?.particleColor = getNoteColor()
        trailEffect?.particleColorBlendFactor = 1.0
        trailEffect?.particleSpeed = 50
        trailEffect?.particleSpeedRange = 20
        trailEffect?.emissionAngle = CGFloat.pi / 2
        trailEffect?.emissionAngleRange = CGFloat.pi / 4
        trailEffect?.zPosition = -2
        
        if let trail = trailEffect {
            addChild(trail)
        }
    }
    
    // MARK: - Visual Helpers
    private func getNoteColor() -> SKColor {
        switch noteData.animalType {
        case .cow:
            return SKColor(red: 0.8, green: 0.6, blue: 0.4, alpha: 1.0) // Brown
        case .pig:
            return SKColor(red: 1.0, green: 0.7, blue: 0.8, alpha: 1.0) // Pink
        case .chicken:
            return SKColor(red: 1.0, green: 0.9, blue: 0.3, alpha: 1.0) // Yellow
        case .sheep:
            return SKColor(red: 0.9, green: 0.9, blue: 0.9, alpha: 1.0) // White
        case .duck:
            return SKColor(red: 0.3, green: 0.7, blue: 1.0, alpha: 1.0) // Blue
        case .goat:
            return SKColor(red: 0.7, green: 0.5, blue: 0.3, alpha: 1.0) // Tan
        case .horse:
            return SKColor(red: 0.5, green: 0.3, blue: 0.1, alpha: 1.0) // Dark brown
        case .rabbit:
            return SKColor(red: 0.8, green: 0.8, blue: 0.8, alpha: 1.0) // Light gray
        case .cat:
            return SKColor(red: 0.4, green: 0.4, blue: 0.4, alpha: 1.0) // Gray
        case .dog:
            return SKColor(red: 0.6, green: 0.4, blue: 0.2, alpha: 1.0) // Brown
        case .turkey:
            return SKColor(red: 0.8, green: 0.4, blue: 0.2, alpha: 1.0) // Orange-brown
        case .llama:
            return SKColor(red: 0.9, green: 0.8, blue: 0.7, alpha: 1.0) // Cream
        }
    }
    
    private func getAnimalTexture() -> SKTexture? {
        // For now, create colored rectangles as placeholders
        // In production, you would load actual animal sprites
        let renderer = UIGraphicsImageRenderer(size: CGSize(width: 40, height: 40))
        let image = renderer.image { context in
            // Draw simple animal representation
            context.cgContext.setFillColor(UIColor.white.cgColor)
            
            switch noteData.animalType {
            case .cow:
                // Draw cow spots
                context.cgContext.fillEllipse(in: CGRect(x: 5, y: 5, width: 8, height: 8))
                context.cgContext.fillEllipse(in: CGRect(x: 20, y: 15, width: 6, height: 6))
                context.cgContext.fillEllipse(in: CGRect(x: 10, y: 25, width: 10, height: 8))
            case .pig:
                // Draw pig snout
                context.cgContext.fillEllipse(in: CGRect(x: 15, y: 15, width: 10, height: 8))
            case .chicken:
                // Draw chicken comb
                let path = UIBezierPath()
                path.move(to: CGPoint(x: 20, y: 5))
                path.addLine(to: CGPoint(x: 15, y: 15))
                path.addLine(to: CGPoint(x: 25, y: 15))
                path.close()
                context.cgContext.addPath(path.cgPath)
                context.cgContext.fillPath()
            default:
                // Default shape
                context.cgContext.fillEllipse(in: CGRect(x: 15, y: 15, width: 10, height: 10))
            }
        }
        
        return SKTexture(image: image)
    }
    
    // MARK: - Hit Animation
    func playHitAnimation(accuracy: HitAccuracy) {
        isHit = true
        
        // Stop trail effect
        trailEffect?.removeFromParent()
        
        // Hit animation based on accuracy
        let hitColor: SKColor
        switch accuracy {
        case .perfect:
            hitColor = .yellow
        case .good:
            hitColor = .green
        case .okay:
            hitColor = .blue
        case .miss:
            hitColor = .red
        }
        
        // Flash effect
        let flashNode = SKSpriteNode(color: hitColor, size: Constants.noteSize)
        flashNode.cornerRadius = Constants.noteSize.width / 2
        flashNode.alpha = 0.8
        flashNode.zPosition = 10
        addChild(flashNode)
        
        let fadeOut = SKAction.fadeOut(withDuration: 0.3)
        let remove = SKAction.removeFromParent()
        let flashSequence = SKAction.sequence([fadeOut, remove])
        flashNode.run(flashSequence)
        
        // Scale animation
        let scaleUp = SKAction.scale(to: 1.3, duration: 0.1)
        let scaleDown = SKAction.scale(to: 0.8, duration: 0.2)
        let scaleSequence = SKAction.sequence([scaleUp, scaleDown])
        
        run(scaleSequence)
        
        // Disable further interactions
        isUserInteractionEnabled = false
    }
    
    // MARK: - Miss Animation
    func playMissAnimation() {
        isHit = true
        
        // Stop trail effect
        trailEffect?.removeFromParent()
        
        // Fade out animation
        let fadeOut = SKAction.fadeOut(withDuration: 0.5)
        let scaleDown = SKAction.scale(to: 0.5, duration: 0.5)
        let missSequence = SKAction.group([fadeOut, scaleDown])
        
        run(missSequence)
        
        // Disable further interactions
        isUserInteractionEnabled = false
    }
    
    // MARK: - Hold Note Support
    func startHoldAnimation() {
        guard noteData.type == .hold else { return }
        
        // Create hold effect
        let holdRing = SKShapeNode(circleOfRadius: Constants.noteSize.width / 2 + 10)
        holdRing.strokeColor = getNoteColor()
        holdRing.lineWidth = 4
        holdRing.fillColor = .clear
        holdRing.alpha = 0.7
        holdRing.zPosition = -1
        addChild(holdRing)
        
        // Animate hold ring
        let pulse = SKAction.sequence([
            SKAction.scale(to: 1.2, duration: 0.3),
            SKAction.scale(to: 1.0, duration: 0.3)
        ])
        let repeatPulse = SKAction.repeatForever(pulse)
        holdRing.run(repeatPulse)
    }
    
    func endHoldAnimation() {
        // Remove hold effects
        children.filter { $0 is SKShapeNode }.forEach { $0.removeFromParent() }
    }
    
    // MARK: - Cleanup
    deinit {
        trailEffect?.removeFromParent()
    }
}

// MARK: - Note Type Extensions
extension NoteNode {
    
    var isSpecialNote: Bool {
        return noteData.type == .special
    }
    
    var isHoldNote: Bool {
        return noteData.type == .hold
    }
    
    var scoreMultiplier: Float {
        switch noteData.type {
        case .normal:
            return 1.0
        case .hold:
            return 1.5
        case .special:
            return 2.0
        }
    }
}

// MARK: - SKSpriteNode Extension for Corner Radius
extension SKSpriteNode {
    var cornerRadius: CGFloat {
        get {
            return 0 // SKSpriteNode doesn't have built-in corner radius
        }
        set {
            // Create a rounded rectangle texture
            let renderer = UIGraphicsImageRenderer(size: size)
            let image = renderer.image { context in
                let rect = CGRect(origin: .zero, size: size)
                let path = UIBezierPath(roundedRect: rect, cornerRadius: newValue)
                color.setFill()
                path.fill()
            }
            texture = SKTexture(image: image)
        }
    }
}
