//
//  RhythmGameScene.swift
//  Moofarm
//
//  Created by Project Moothew on 28/7/25.
//

import SpriteKit
import GameplayKit
import Combine

/// Main SpriteKit scene for rhythm game
/// Handles note spawning, touch detection, scoring, and visual effects
class RhythmGameScene: SKScene {
    
    // MARK: - Constants
    private struct Constants {
        static let laneCount = 4
        static let noteSpeed: CGFloat = 200.0 // points per second
        static let hitZoneHeight: CGFloat = 80.0
        static let noteSize = CGSize(width: 60, height: 60)
        static let laneWidth: CGFloat = 80.0
        static let hitTolerance: TimeInterval = 0.15 // 150ms tolerance
    }
    
    // MARK: - Game State
    private var gameState: RhythmGameState = .idle
    private var currentSong: Song?
    private var songStartTime: TimeInterval = 0
    private var currentTime: TimeInterval = 0
    private var isGameActive = false
    
    // MARK: - Scene Nodes
    private var backgroundNode: SKSpriteNode!
    private var laneNodes: [SKSpriteNode] = []
    private var hitZoneNodes: [SKSpriteNode] = []
    private var noteContainer: SKNode!
    private var effectsContainer: SKNode!
    private var uiContainer: SKNode!
    
    // MARK: - Game Objects
    private var activeNotes: [NoteNode] = []
    private var noteQueue: [NoteData] = []
    private var nextNoteIndex = 0
    
    // MARK: - Scoring
    private var currentScore = 0
    private var currentCombo = 0
    private var maxCombo = 0
    private var perfectHits = 0
    private var goodHits = 0
    private var okayHits = 0
    private var missedHits = 0
    private var multiplier: Float = 1.0
    
    // MARK: - UI Elements
    private var scoreLabel: SKLabelNode!
    private var comboLabel: SKLabelNode!
    private var multiplierLabel: SKLabelNode!
    private var progressBar: SKSpriteNode!
    
    // MARK: - Audio Integration
    private var audioService: AudioService?
    private var beatSubscription: AnyCancellable?
    
    // MARK: - Delegates
    weak var gameDelegate: RhythmGameSceneDelegate?
    
    // MARK: - Initialization
    override func didMove(to view: SKView) {
        super.didMove(to: view)
        setupScene()
        setupNodes()
        setupUI()
    }
    
    // MARK: - Scene Setup
    private func setupScene() {
        backgroundColor = SKColor(red: 0.1, green: 0.1, blue: 0.2, alpha: 1.0)
        physicsWorld.gravity = CGVector.zero
        
        // Setup containers
        noteContainer = SKNode()
        addChild(noteContainer)
        
        effectsContainer = SKNode()
        addChild(effectsContainer)
        
        uiContainer = SKNode()
        addChild(uiContainer)
    }
    
    private func setupNodes() {
        setupBackground()
        setupLanes()
        setupHitZones()
    }
    
    private func setupBackground() {
        backgroundNode = SKSpriteNode(color: SKColor(red: 0.05, green: 0.15, blue: 0.05, alpha: 1.0), size: size)
        backgroundNode.position = CGPoint(x: size.width / 2, y: size.height / 2)
        backgroundNode.zPosition = -10
        addChild(backgroundNode)
    }
    
    private func setupLanes() {
        let totalLaneWidth = Constants.laneWidth * CGFloat(Constants.laneCount)
        let startX = (size.width - totalLaneWidth) / 2 + Constants.laneWidth / 2
        
        for i in 0..<Constants.laneCount {
            let laneX = startX + CGFloat(i) * Constants.laneWidth
            
            // Lane background
            let laneNode = SKSpriteNode(color: SKColor.white.withAlphaComponent(0.1), 
                                       size: CGSize(width: Constants.laneWidth - 4, height: size.height))
            laneNode.position = CGPoint(x: laneX, y: size.height / 2)
            laneNode.zPosition = -5
            addChild(laneNode)
            laneNodes.append(laneNode)
        }
    }
    
    private func setupHitZones() {
        let hitZoneY = Constants.hitZoneHeight / 2 + 50 // Bottom of screen + padding
        
        for i in 0..<Constants.laneCount {
            let laneX = laneNodes[i].position.x
            
            let hitZone = SKSpriteNode(color: SKColor.yellow.withAlphaComponent(0.3),
                                     size: CGSize(width: Constants.laneWidth - 8, height: Constants.hitZoneHeight))
            hitZone.position = CGPoint(x: laneX, y: hitZoneY)
            hitZone.zPosition = 1
            addChild(hitZone)
            hitZoneNodes.append(hitZone)
        }
    }
    
    private func setupUI() {
        // Score label
        scoreLabel = SKLabelNode(fontNamed: "AvenirNext-Bold")
        scoreLabel.text = "Score: 0"
        scoreLabel.fontSize = 24
        scoreLabel.fontColor = .white
        scoreLabel.position = CGPoint(x: 100, y: size.height - 60)
        scoreLabel.zPosition = 100
        uiContainer.addChild(scoreLabel)
        
        // Combo label
        comboLabel = SKLabelNode(fontNamed: "AvenirNext-Bold")
        comboLabel.text = "Combo: 0"
        comboLabel.fontSize = 20
        comboLabel.fontColor = .yellow
        comboLabel.position = CGPoint(x: 100, y: size.height - 90)
        comboLabel.zPosition = 100
        uiContainer.addChild(comboLabel)
        
        // Multiplier label
        multiplierLabel = SKLabelNode(fontNamed: "AvenirNext-Bold")
        multiplierLabel.text = "x1.0"
        multiplierLabel.fontSize = 18
        multiplierLabel.fontColor = .cyan
        multiplierLabel.position = CGPoint(x: size.width - 100, y: size.height - 60)
        multiplierLabel.zPosition = 100
        uiContainer.addChild(multiplierLabel)
        
        // Progress bar background
        let progressBG = SKSpriteNode(color: .darkGray, size: CGSize(width: size.width - 40, height: 8))
        progressBG.position = CGPoint(x: size.width / 2, y: size.height - 30)
        progressBG.zPosition = 99
        uiContainer.addChild(progressBG)
        
        // Progress bar
        progressBar = SKSpriteNode(color: .green, size: CGSize(width: 0, height: 6))
        progressBar.anchorPoint = CGPoint(x: 0, y: 0.5)
        progressBar.position = CGPoint(x: 20, y: size.height - 30)
        progressBar.zPosition = 100
        uiContainer.addChild(progressBar)
    }
    
    // MARK: - Game Control
    func startGame(with song: Song, audioService: AudioService) {
        guard gameState == .idle else { return }
        
        self.currentSong = song
        self.audioService = audioService
        
        // Reset game state
        resetGameState()
        
        // Load note data
        loadNoteData(for: song)
        
        // Start audio
        let songFileName = song.fileName?.replacingOccurrences(of: ".mp3", with: "") ?? "default_song"
        audioService.playMusic(songFileName, loop: false)
            .sink(
                receiveCompletion: { completion in
                    if case .failure(let error) = completion {
                        print("Failed to play song: \(error)")
                    }
                },
                receiveValue: { [weak self] in
                    self?.startGameplay()
                }
            )
            .store(in: &Set<AnyCancellable>())
    }
    
    private func startGameplay() {
        gameState = .playing
        isGameActive = true
        songStartTime = currentTime
        
        // Setup beat detection
        if let song = currentSong, let audioService = audioService {
            beatSubscription = audioService.startBeatDetection(bpm: song.bpm)
                .sink { [weak self] in
                    self?.onBeatDetected()
                }
        }
        
        gameDelegate?.gameDidStart()
    }
    
    func pauseGame() {
        guard gameState == .playing else { return }
        
        gameState = .paused
        isGameActive = false
        isPaused = true
        
        audioService?.stopMusic()
        beatSubscription?.cancel()
        
        gameDelegate?.gameDidPause()
    }
    
    func resumeGame() {
        guard gameState == .paused else { return }
        
        gameState = .playing
        isGameActive = true
        isPaused = false
        
        // Resume audio and beat detection
        if let song = currentSong, let audioService = audioService {
            let songFileName = song.fileName?.replacingOccurrences(of: ".mp3", with: "") ?? "default_song"
            _ = audioService.playMusic(songFileName, loop: false)
            
            beatSubscription = audioService.startBeatDetection(bpm: song.bpm)
                .sink { [weak self] in
                    self?.onBeatDetected()
                }
        }
        
        gameDelegate?.gameDidResume()
    }
    
    func endGame() {
        gameState = .completed
        isGameActive = false
        
        audioService?.stopMusic()
        beatSubscription?.cancel()
        
        // Calculate final score
        let finalScore = ScoreData(
            totalScore: currentScore,
            combo: maxCombo,
            accuracy: calculateAccuracy(),
            perfectHits: perfectHits,
            goodHits: goodHits,
            okayHits: okayHits,
            missedHits: missedHits,
            coinsEarned: calculateCoinsEarned(),
            experienceEarned: calculateExperienceEarned()
        )
        
        gameDelegate?.gameDidEnd(with: finalScore)
    }
    
    // MARK: - Game State Management
    private func resetGameState() {
        currentScore = 0
        currentCombo = 0
        maxCombo = 0
        perfectHits = 0
        goodHits = 0
        okayHits = 0
        missedHits = 0
        multiplier = 1.0
        nextNoteIndex = 0
        
        // Clear existing notes
        activeNotes.removeAll()
        noteContainer.removeAllChildren()
        effectsContainer.removeAllChildren()
        
        updateUI()
    }
    
    private func loadNoteData(for song: Song) {
        // For now, generate simple note pattern
        // In production, this would load from song.noteData
        noteQueue = generateNotePattern(for: song)
    }
    
    private func generateNotePattern(for song: Song) -> [NoteData] {
        var notes: [NoteData] = []
        let duration = song.duration
        let bpm = song.bpm
        let beatsPerSecond = bpm / 60.0
        let noteInterval = 1.0 / beatsPerSecond
        
        var currentTime: Double = 2.0 // Start after 2 seconds
        
        while currentTime < duration - 2.0 {
            let lane = Int.random(in: 0..<Constants.laneCount)
            let animalType = AnimalType.allCases.randomElement() ?? .cow
            
            let note = NoteData(
                lane: lane,
                timing: currentTime,
                type: .normal,
                animalType: animalType
            )
            notes.append(note)
            
            // Vary the interval slightly for more interesting patterns
            currentTime += noteInterval * Double.random(in: 0.8...1.2)
        }
        
        return notes.sorted { $0.timing < $1.timing }
    }
    
    // MARK: - Update Loop
    override func update(_ currentTime: TimeInterval) {
        super.update(currentTime)
        
        self.currentTime = currentTime
        
        guard isGameActive else { return }
        
        let gameTime = currentTime - songStartTime
        
        // Spawn new notes
        spawnNotes(at: gameTime)
        
        // Update existing notes
        updateNotes(deltaTime: currentTime)
        
        // Check for missed notes
        checkMissedNotes()
        
        // Update progress bar
        updateProgressBar(gameTime: gameTime)
        
        // Check if song is complete
        if let song = currentSong, gameTime >= song.duration {
            endGame()
        }
    }
    
    private func spawnNotes(at gameTime: TimeInterval) {
        let spawnTime = 2.0 // Spawn notes 2 seconds before they need to be hit
        
        while nextNoteIndex < noteQueue.count {
            let noteData = noteQueue[nextNoteIndex]
            let timeToHit = noteData.timing - gameTime
            
            if timeToHit <= spawnTime {
                spawnNote(noteData)
                nextNoteIndex += 1
            } else {
                break
            }
        }
    }
    
    private func spawnNote(_ noteData: NoteData) {
        let laneX = laneNodes[noteData.lane].position.x
        let startY = size.height + Constants.noteSize.height
        
        let noteNode = NoteNode(noteData: noteData)
        noteNode.position = CGPoint(x: laneX, y: startY)
        noteNode.zPosition = 10
        
        noteContainer.addChild(noteNode)
        activeNotes.append(noteNode)
        
        // Animate note movement
        let hitZoneY = hitZoneNodes[noteData.lane].position.y
        let distance = startY - hitZoneY
        let duration = TimeInterval(distance / Constants.noteSpeed)
        
        let moveAction = SKAction.moveTo(y: hitZoneY - 100, duration: duration) // Move past hit zone
        let removeAction = SKAction.removeFromParent()
        let sequence = SKAction.sequence([moveAction, removeAction])
        
        noteNode.run(sequence) { [weak self] in
            self?.removeNote(noteNode)
        }
    }
    
    private func updateNotes(deltaTime: TimeInterval) {
        // Notes are updated by their SKActions
        // This method can be used for additional note logic if needed
    }
    
    private func checkMissedNotes() {
        let hitZoneY = hitZoneNodes[0].position.y
        let missThreshold = hitZoneY - Constants.hitZoneHeight
        
        for note in activeNotes {
            if note.position.y < missThreshold && !note.isHit {
                handleMiss(note)
            }
        }
    }
    
    private func removeNote(_ note: NoteNode) {
        if let index = activeNotes.firstIndex(of: note) {
            activeNotes.remove(at: index)
        }
        note.removeFromParent()
    }
}

// MARK: - Touch Handling
extension RhythmGameScene {
    
    override func touchesBegan(_ touches: Set<UITouch>, with event: UIEvent?) {
        guard isGameActive else { return }
        
        for touch in touches {
            let location = touch.location(in: self)
            handleTouch(at: location)
        }
    }
    
    private func handleTouch(at location: CGPoint) {
        // Determine which lane was touched
        guard let lane = getLaneForTouch(at: location) else { return }
        
        // Find the closest note in this lane
        let notesInLane = activeNotes.filter { $0.noteData.lane == lane && !$0.isHit }
        
        guard let closestNote = getClosestNoteToHitZone(in: notesInLane, lane: lane) else {
            // No note to hit, but still provide feedback
            createMissEffect(at: CGPoint(x: laneNodes[lane].position.x, y: hitZoneNodes[lane].position.y))
            return
        }
        
        // Calculate hit accuracy
        let hitZoneY = hitZoneNodes[lane].position.y
        let distance = abs(closestNote.position.y - hitZoneY)
        let accuracy = calculateHitAccuracy(distance: distance)
        
        // Process the hit
        processHit(note: closestNote, accuracy: accuracy)
    }
    
    private func getLaneForTouch(at location: CGPoint) -> Int? {
        for (index, laneNode) in laneNodes.enumerated() {
            let laneRect = CGRect(
                x: laneNode.position.x - Constants.laneWidth / 2,
                y: 0,
                width: Constants.laneWidth,
                height: size.height
            )
            
            if laneRect.contains(location) {
                return index
            }
        }
        return nil
    }
    
    private func getClosestNoteToHitZone(in notes: [NoteNode], lane: Int) -> NoteNode? {
        let hitZoneY = hitZoneNodes[lane].position.y
        let maxDistance = Constants.hitZoneHeight * 2 // Allow hits within 2x hit zone height
        
        return notes
            .filter { abs($0.position.y - hitZoneY) <= maxDistance }
            .min { abs($0.position.y - hitZoneY) < abs($1.position.y - hitZoneY) }
    }
    
    private func calculateHitAccuracy(distance: CGFloat) -> HitAccuracy {
        let normalizedDistance = distance / Constants.hitZoneHeight
        
        switch normalizedDistance {
        case 0...0.2:
            return .perfect
        case 0.2...0.5:
            return .good
        case 0.5...0.8:
            return .okay
        default:
            return .miss
        }
    }
    
    private func processHit(note: NoteNode, accuracy: HitAccuracy) {
        note.isHit = true
        
        // Update statistics
        switch accuracy {
        case .perfect:
            perfectHits += 1
            currentCombo += 1
        case .good:
            goodHits += 1
            currentCombo += 1
        case .okay:
            okayHits += 1
            currentCombo += 1
        case .miss:
            missedHits += 1
            currentCombo = 0
        }
        
        // Update max combo
        maxCombo = max(maxCombo, currentCombo)
        
        // Calculate score
        let baseScore = accuracy.baseScore
        let comboBonus = min(currentCombo / 10, 5) // Max 5x combo bonus
        let finalScore = Int(Float(baseScore + comboBonus) * multiplier)
        currentScore += finalScore
        
        // Update multiplier based on combo
        updateMultiplier()
        
        // Create visual effects
        createHitEffect(at: note.position, accuracy: accuracy, score: finalScore)
        
        // Play sound effect
        playHitSound(for: note.noteData.animalType, accuracy: accuracy)
        
        // Remove note
        removeNote(note)
        
        // Update UI
        updateUI()
    }
    
    private func handleMiss(_ note: NoteNode) {
        guard !note.isHit else { return }

        note.isHit = true
        missedHits += 1
        currentCombo = 0
        multiplier = 1.0

        createMissEffect(at: note.position)
        removeNote(note)
        updateUI()
    }
}

// MARK: - Visual Effects
extension RhythmGameScene {

    private func createHitEffect(at position: CGPoint, accuracy: HitAccuracy, score: Int) {
        // Create hit effect based on accuracy
        let effectColor: SKColor
        let effectText: String

        switch accuracy {
        case .perfect:
            effectColor = .yellow
            effectText = "PERFECT!"
        case .good:
            effectColor = .green
            effectText = "GOOD"
        case .okay:
            effectColor = .blue
            effectText = "OKAY"
        case .miss:
            effectColor = .red
            effectText = "MISS"
        }

        // Create text effect
        let textNode = SKLabelNode(fontNamed: "AvenirNext-Bold")
        textNode.text = effectText
        textNode.fontSize = 20
        textNode.fontColor = effectColor
        textNode.position = position
        textNode.zPosition = 50
        effectsContainer.addChild(textNode)

        // Create score text
        let scoreNode = SKLabelNode(fontNamed: "AvenirNext-Medium")
        scoreNode.text = "+\(score)"
        scoreNode.fontSize = 16
        scoreNode.fontColor = .white
        scoreNode.position = CGPoint(x: position.x, y: position.y - 25)
        scoreNode.zPosition = 50
        effectsContainer.addChild(scoreNode)

        // Animate effects
        let fadeOut = SKAction.fadeOut(withDuration: 0.5)
        let moveUp = SKAction.moveBy(x: 0, y: 30, duration: 0.5)
        let scale = SKAction.scale(to: 1.2, duration: 0.1)
        let scaleBack = SKAction.scale(to: 1.0, duration: 0.4)
        let remove = SKAction.removeFromParent()

        let textSequence = SKAction.sequence([
            scale,
            SKAction.group([scaleBack, moveUp, fadeOut]),
            remove
        ])

        textNode.run(textSequence)
        scoreNode.run(SKAction.sequence([moveUp, fadeOut, remove]))

        // Create particle effect for perfect hits
        if accuracy == .perfect {
            createPerfectHitParticles(at: position)
        }

        // Screen shake for good hits
        if accuracy == .perfect || accuracy == .good {
            createScreenShake()
        }
    }

    private func createMissEffect(at position: CGPoint) {
        let missNode = SKLabelNode(fontNamed: "AvenirNext-Bold")
        missNode.text = "MISS"
        missNode.fontSize = 18
        missNode.fontColor = .red
        missNode.position = position
        missNode.zPosition = 50
        effectsContainer.addChild(missNode)

        let fadeOut = SKAction.fadeOut(withDuration: 0.3)
        let remove = SKAction.removeFromParent()
        let sequence = SKAction.sequence([fadeOut, remove])

        missNode.run(sequence)
    }

    private func createPerfectHitParticles(at position: CGPoint) {
        // Create simple particle effect
        for _ in 0..<8 {
            let particle = SKSpriteNode(color: .yellow, size: CGSize(width: 4, height: 4))
            particle.position = position
            particle.zPosition = 45
            effectsContainer.addChild(particle)

            let angle = Double.random(in: 0...(2 * Double.pi))
            let distance = CGFloat.random(in: 20...40)
            let endX = position.x + cos(angle) * distance
            let endY = position.y + sin(angle) * distance

            let move = SKAction.move(to: CGPoint(x: endX, y: endY), duration: 0.3)
            let fade = SKAction.fadeOut(withDuration: 0.3)
            let remove = SKAction.removeFromParent()
            let sequence = SKAction.sequence([SKAction.group([move, fade]), remove])

            particle.run(sequence)
        }
    }

    private func createScreenShake() {
        let shakeAmount: CGFloat = 5.0
        let shakeDuration: TimeInterval = 0.1

        let shakeLeft = SKAction.moveBy(x: -shakeAmount, y: 0, duration: shakeDuration / 4)
        let shakeRight = SKAction.moveBy(x: shakeAmount * 2, y: 0, duration: shakeDuration / 2)
        let shakeBack = SKAction.moveBy(x: -shakeAmount, y: 0, duration: shakeDuration / 4)

        let shakeSequence = SKAction.sequence([shakeLeft, shakeRight, shakeBack])
        camera?.run(shakeSequence)
    }
}

// MARK: - Audio Integration
extension RhythmGameScene {

    private func onBeatDetected() {
        // Visual beat indicator
        createBeatPulse()
    }

    private func createBeatPulse() {
        // Pulse the hit zones on beat
        for hitZone in hitZoneNodes {
            let pulse = SKAction.sequence([
                SKAction.scale(to: 1.1, duration: 0.1),
                SKAction.scale(to: 1.0, duration: 0.1)
            ])
            hitZone.run(pulse)
        }
    }

    private func playHitSound(for animalType: AnimalType, accuracy: HitAccuracy) {
        guard let audioService = audioService else { return }

        // Play animal sound
        _ = audioService.playAnimalSound(animalType.rawValue)

        // Play hit sound based on accuracy
        let hitSound: UISound
        switch accuracy {
        case .perfect:
            hitSound = .perfectHit
        case .good, .okay:
            hitSound = .success
        case .miss:
            hitSound = .error
        }

        _ = audioService.playUISound(hitSound)
    }
}

// MARK: - UI Updates
extension RhythmGameScene {

    private func updateUI() {
        scoreLabel.text = "Score: \(currentScore)"
        comboLabel.text = "Combo: \(currentCombo)"
        multiplierLabel.text = "x\(String(format: "%.1f", multiplier))"

        // Animate combo label for high combos
        if currentCombo > 0 && currentCombo % 10 == 0 {
            let pulse = SKAction.sequence([
                SKAction.scale(to: 1.2, duration: 0.1),
                SKAction.scale(to: 1.0, duration: 0.1)
            ])
            comboLabel.run(pulse)
        }
    }

    private func updateMultiplier() {
        let newMultiplier = min(3.0, 1.0 + Float(currentCombo) * 0.05) // Max 3x multiplier

        if newMultiplier != multiplier {
            multiplier = newMultiplier

            // Animate multiplier change
            let pulse = SKAction.sequence([
                SKAction.scale(to: 1.3, duration: 0.1),
                SKAction.scale(to: 1.0, duration: 0.1)
            ])
            multiplierLabel.run(pulse)
        }
    }

    private func updateProgressBar(gameTime: TimeInterval) {
        guard let song = currentSong else { return }

        let progress = min(1.0, gameTime / song.duration)
        let maxWidth = size.width - 40
        let newWidth = maxWidth * CGFloat(progress)

        progressBar.size.width = newWidth

        // Change color based on progress
        if progress > 0.8 {
            progressBar.color = .red
        } else if progress > 0.5 {
            progressBar.color = .yellow
        } else {
            progressBar.color = .green
        }
    }
}

// MARK: - Score Calculation
extension RhythmGameScene {

    private func calculateAccuracy() -> Double {
        let totalHits = perfectHits + goodHits + okayHits + missedHits
        guard totalHits > 0 else { return 0.0 }

        let successfulHits = perfectHits + goodHits + okayHits
        return Double(successfulHits) / Double(totalHits) * 100.0
    }

    private func calculateCoinsEarned() -> Int {
        let baseCoins = currentScore / 1000 // 1 coin per 1000 points
        let comboBonus = maxCombo / 10 // 1 coin per 10 max combo
        let accuracyBonus = Int(calculateAccuracy() / 10) // 1 coin per 10% accuracy

        return max(1, baseCoins + comboBonus + accuracyBonus)
    }

    private func calculateExperienceEarned() -> Int {
        let baseXP = currentScore / 500 // 1 XP per 500 points
        let perfectBonus = perfectHits / 5 // 1 XP per 5 perfect hits
        let completionBonus = gameState == .completed ? 10 : 0

        return max(1, baseXP + perfectBonus + completionBonus)
    }
}

// MARK: - Delegate Protocol
protocol RhythmGameSceneDelegate: AnyObject {
    func gameDidStart()
    func gameDidPause()
    func gameDidResume()
    func gameDidEnd(with scoreData: ScoreData)
}
