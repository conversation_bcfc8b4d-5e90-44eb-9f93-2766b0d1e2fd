//
//  MainMenuView.swift
//  Moofarm
//
//  Created by Project Moothew on 28/7/25.
//

import SwiftUI

struct MainMenuView: View {
    
    // MARK: - Environment
    @EnvironmentObject private var audioService: AudioService
    @EnvironmentObject private var gameKitService: GameKitService
    
    // MARK: - State
    @State private var currentGameState: GameState = .menu
    @State private var showSettings = false
    @State private var showAnimalCollection = false
    @State private var isLoading = false
    
    // MARK: - Body
    var body: some View {
        NavigationStack {
            ZStack {
                // Background
                backgroundView
                
                // Main Content
                VStack(spacing: 30) {
                    // Title
                    titleSection
                    
                    // Menu Buttons
                    menuButtonsSection
                    
                    // Player Info
                    playerInfoSection
                    
                    Spacer()
                }
                .padding(.horizontal, 20)
                .padding(.top, 60)
                
                // Loading Overlay
                if isLoading {
                    loadingOverlay
                }
            }
            .navigationBarHidden(true)
            .onAppear {
                setupMainMenu()
            }
        }
        .sheet(isPresented: $showSettings) {
            SettingsView()
        }
        .sheet(isPresented: $showAnimalCollection) {
            AnimalCollectionView()
        }
    }
    
    // MARK: - Background
    private var backgroundView: some View {
        LinearGradient(
            gradient: Gradient(colors: [
                Color(red: 0.85, green: 0.95, blue: 0.85),  // Light mint
                Color(red: 0.95, green: 0.90, blue: 0.85)   // Light peach
            ]),
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
        .ignoresSafeArea()
    }
    
    // MARK: - Title Section
    private var titleSection: some View {
        VStack(spacing: 10) {
            Text("🐄 Moo Farm")
                .font(.system(size: 48, weight: .bold, design: .rounded))
                .foregroundColor(.primary)
                .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 2)
            
            Text("Music & Build")
                .font(.system(size: 20, weight: .medium, design: .rounded))
                .foregroundColor(.secondary)
            
            Text("Nông Trại Âm Nhạc")
                .font(.system(size: 16, weight: .regular, design: .rounded))
                .foregroundColor(.secondary)
                .opacity(0.8)
        }
    }
    
    // MARK: - Menu Buttons
    private var menuButtonsSection: some View {
        VStack(spacing: 20) {
            // Play Rhythm Game
            MenuButton(
                title: "🎵 Play Rhythm Game",
                subtitle: "Chơi Game Nhịp Điệu",
                backgroundColor: .blue,
                action: {
                    playRhythmGame()
                }
            )
            
            // Build Farm
            MenuButton(
                title: "🏡 Build Farm",
                subtitle: "Xây Dựng Nông Trại",
                backgroundColor: .green,
                action: {
                    buildFarm()
                }
            )
            
            // Animal Collection
            MenuButton(
                title: "🐾 Animal Collection",
                subtitle: "Bộ Sưu Tập Động Vật",
                backgroundColor: .orange,
                action: {
                    showAnimalCollection = true
                }
            )
            
            // Settings
            MenuButton(
                title: "⚙️ Settings",
                subtitle: "Cài Đặt",
                backgroundColor: .gray,
                action: {
                    showSettings = true
                }
            )
        }
    }
    
    // MARK: - Player Info
    private var playerInfoSection: some View {
        VStack(spacing: 8) {
            if gameKitService.isAuthenticated {
                HStack {
                    Image(systemName: "person.circle.fill")
                        .foregroundColor(.blue)
                    Text(gameKitService.playerDisplayName)
                        .font(.system(size: 16, weight: .medium, design: .rounded))
                }
            } else {
                Button("Connect to Game Center") {
                    authenticateGameCenter()
                }
                .font(.system(size: 14, weight: .medium, design: .rounded))
                .foregroundColor(.blue)
            }
            
            Text("Project Moothew - v1.0")
                .font(.system(size: 12, weight: .regular, design: .rounded))
                .foregroundColor(.secondary)
                .opacity(0.6)
        }
    }
    
    // MARK: - Loading Overlay
    private var loadingOverlay: some View {
        ZStack {
            Color.black.opacity(0.3)
                .ignoresSafeArea()
            
            VStack(spacing: 20) {
                ProgressView()
                    .scaleEffect(1.5)
                    .tint(.white)
                
                Text("Loading...")
                    .font(.system(size: 18, weight: .medium, design: .rounded))
                    .foregroundColor(.white)
            }
            .padding(40)
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(.ultraThinMaterial)
            )
        }
    }
    
    // MARK: - Actions
    private func setupMainMenu() {
        // Play background music
        _ = audioService.playMusic("menu_background_music", loop: true)
        
        // Play UI sound
        _ = audioService.playUISound(.tap)
    }
    
    private func playRhythmGame() {
        _ = audioService.playUISound(.tap)
        currentGameState = .rhythmGame
        
        // TODO: Navigate to Rhythm Game
        print("🎵 Navigating to Rhythm Game")
    }
    
    private func buildFarm() {
        _ = audioService.playUISound(.tap)
        currentGameState = .farm
        
        // TODO: Navigate to Farm
        print("🏡 Navigating to Farm")
    }
    
    private func authenticateGameCenter() {
        isLoading = true
        
        _ = gameKitService.authenticatePlayer()
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { completion in
                    self.isLoading = false
                    if case .failure(let error) = completion {
                        print("GameKit authentication failed: \(error)")
                    }
                },
                receiveValue: { success in
                    if success {
                        _ = self.audioService.playUISound(.success)
                    }
                }
            )
    }
}

// MARK: - Menu Button Component
struct MenuButton: View {
    let title: String
    let subtitle: String
    let backgroundColor: Color
    let action: () -> Void
    
    @State private var isPressed = false
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 8) {
                Text(title)
                    .font(.system(size: 20, weight: .bold, design: .rounded))
                    .foregroundColor(.white)
                
                Text(subtitle)
                    .font(.system(size: 14, weight: .medium, design: .rounded))
                    .foregroundColor(.white.opacity(0.9))
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 20)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(backgroundColor)
                    .shadow(color: backgroundColor.opacity(0.3), radius: 8, x: 0, y: 4)
            )
            .scaleEffect(isPressed ? 0.95 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: isPressed)
        }
        .buttonStyle(PlainButtonStyle())
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            isPressed = pressing
        }, perform: {})
    }
}

// MARK: - Placeholder Views
struct SettingsView: View {
    var body: some View {
        NavigationView {
            VStack {
                Text("⚙️ Settings")
                    .font(.largeTitle)
                    .padding()
                
                Text("Settings will be implemented here")
                    .foregroundColor(.secondary)
                
                Spacer()
            }
            .navigationTitle("Settings")
            .navigationBarTitleDisplayMode(.inline)
        }
    }
}

struct AnimalCollectionView: View {
    var body: some View {
        NavigationView {
            VStack {
                Text("🐾 Animal Collection")
                    .font(.largeTitle)
                    .padding()
                
                Text("Animal collection will be implemented here")
                    .foregroundColor(.secondary)
                
                Spacer()
            }
            .navigationTitle("Animal Collection")
            .navigationBarTitleDisplayMode(.inline)
        }
    }
}

// MARK: - Preview
#Preview {
    MainMenuView()
        .environmentObject(AudioService.shared)
        .environmentObject(GameKitService.shared)
}
