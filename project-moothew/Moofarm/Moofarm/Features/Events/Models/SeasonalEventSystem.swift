//
//  SeasonalEventSystem.swift
//  Moofarm
//
//  Created by Project Moothew on 28/7/25.
//

import Foundation
import Combine

/// Seasonal events system with time-limited content and rewards
/// Provides rotating events, special challenges, and exclusive items
class SeasonalEventSystem: ObservableObject {
    
    // MARK: - Published Properties
    @Published var currentEvent: SeasonalEvent?
    @Published var upcomingEvents: [SeasonalEvent] = []
    @Published var eventProgress: [String: EventProgress] = [:]
    @Published var availableRewards: [EventReward] = []
    @Published var unlockedRewards: Set<String> = []
    @Published var eventChallenges: [EventChallenge] = []
    
    // MARK: - Services
    private let dataService: DataService
    private let gameManager: GameManager
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Timer
    private var eventTimer: Timer?
    
    // MARK: - Initialization
    init(
        dataService: DataService = DataService.shared,
        gameManager: GameManager = GameManager.shared
    ) {
        self.dataService = dataService
        self.gameManager = gameManager
        
        setupEventSchedule()
        startEventTimer()
    }
    
    deinit {
        eventTimer?.invalidate()
    }
    
    // MARK: - Event Schedule
    private func setupEventSchedule() {
        let now = Date()
        let calendar = Calendar.current
        
        // Generate seasonal events for the year
        let events = generateSeasonalEvents(for: calendar.component(.year, from: now))
        
        // Find current and upcoming events
        currentEvent = events.first { event in
            now >= event.startDate && now <= event.endDate
        }
        
        upcomingEvents = events.filter { event in
            event.startDate > now
        }.sorted { $0.startDate < $1.startDate }
        
        // Load event progress
        loadEventProgress()
        
        // Setup current event
        if let current = currentEvent {
            setupCurrentEvent(current)
        }
        
        print("🎪 Seasonal Events initialized - Current: \(currentEvent?.name ?? "None")")
    }
    
    private func generateSeasonalEvents(for year: Int) -> [SeasonalEvent] {
        let calendar = Calendar.current
        
        return [
            // Spring Event
            SeasonalEvent(
                id: "spring_festival_\(year)",
                name: "Spring Harmony Festival",
                description: "Celebrate the blooming season with special farm decorations and rhythm challenges!",
                type: .seasonal,
                theme: .spring,
                startDate: calendar.date(from: DateComponents(year: year, month: 3, day: 20)) ?? Date(),
                endDate: calendar.date(from: DateComponents(year: year, month: 4, day: 20)) ?? Date(),
                rewards: [],
                challenges: [],
                specialFeatures: [
                    "Cherry Blossom Decorations",
                    "Spring Animal Variants",
                    "Flower Power Rhythm Songs"
                ]
            ),
            
            // Summer Event
            SeasonalEvent(
                id: "summer_beats_\(year)",
                name: "Summer Beats Carnival",
                description: "Dance to the summer rhythm with beach-themed content and tropical animals!",
                type: .seasonal,
                theme: .summer,
                startDate: calendar.date(from: DateComponents(year: year, month: 6, day: 21)) ?? Date(),
                endDate: calendar.date(from: DateComponents(year: year, month: 7, day: 21)) ?? Date(),
                rewards: [],
                challenges: [],
                specialFeatures: [
                    "Beach Farm Theme",
                    "Tropical Animal Skins",
                    "Summer Hit Songs"
                ]
            ),
            
            // Halloween Event
            SeasonalEvent(
                id: "spooky_harvest_\(year)",
                name: "Spooky Harvest Festival",
                description: "A frightfully fun event with spooky decorations and mysterious animals!",
                type: .holiday,
                theme: .halloween,
                startDate: calendar.date(from: DateComponents(year: year, month: 10, day: 15)) ?? Date(),
                endDate: calendar.date(from: DateComponents(year: year, month: 11, day: 5)) ?? Date(),
                rewards: [],
                challenges: [],
                specialFeatures: [
                    "Spooky Farm Decorations",
                    "Ghost Animal Variants",
                    "Horror-themed Rhythm Songs"
                ]
            ),
            
            // Winter/Christmas Event
            SeasonalEvent(
                id: "winter_wonderland_\(year)",
                name: "Winter Wonderland",
                description: "Celebrate the holidays with snow-covered farms and festive music!",
                type: .holiday,
                theme: .winter,
                startDate: calendar.date(from: DateComponents(year: year, month: 12, day: 15)) ?? Date(),
                endDate: calendar.date(from: DateComponents(year: year + 1, month: 1, day: 15)) ?? Date(),
                rewards: [],
                challenges: [],
                specialFeatures: [
                    "Snow-covered Farm Theme",
                    "Holiday Animal Costumes",
                    "Christmas Carol Remixes"
                ]
            )
        ]
    }
    
    // MARK: - Event Management
    private func setupCurrentEvent(_ event: SeasonalEvent) {
        // Load event-specific content
        availableRewards = event.rewards
        eventChallenges = event.challenges
        
        // Initialize event progress
        if eventProgress[event.id] == nil {
            eventProgress[event.id] = EventProgress(
                eventId: event.id,
                pointsEarned: 0,
                challengesCompleted: 0,
                rewardsUnlocked: [],
                participationDays: 0,
                lastParticipationDate: nil
            )
        }
        
        // Apply event theme
        applyEventTheme(event.theme)
        
        print("🎪 Started event: \(event.name)")
    }
    
    private func applyEventTheme(_ theme: EventTheme) {
        // Apply visual theme changes
        switch theme {
        case .spring:
            // Apply spring colors and decorations
            break
        case .summer:
            // Apply summer beach theme
            break
        case .halloween:
            // Apply spooky theme
            break
        case .winter:
            // Apply winter/snow theme
            break
        case .special:
            // Apply special event theme
            break
        }
        
        print("🎨 Applied \(theme.rawValue) theme")
    }
    
    // MARK: - Event Timer
    private func startEventTimer() {
        eventTimer = Timer.scheduledTimer(withTimeInterval: 3600, repeats: true) { [weak self] _ in
            self?.checkEventStatus()
        }
    }
    
    private func checkEventStatus() {
        let now = Date()
        
        // Check if current event has ended
        if let current = currentEvent, now > current.endDate {
            endCurrentEvent()
        }
        
        // Check for new events starting
        if currentEvent == nil {
            if let newEvent = upcomingEvents.first(where: { now >= $0.startDate && now <= $0.endDate }) {
                currentEvent = newEvent
                upcomingEvents.removeAll { $0.id == newEvent.id }
                setupCurrentEvent(newEvent)
            }
        }
        
        // Update daily participation
        updateDailyParticipation()
    }
    
    private func endCurrentEvent() {
        guard let event = currentEvent else { return }
        
        print("🎪 Event ended: \(event.name)")
        
        // Clean up event-specific content
        currentEvent = nil
        availableRewards.removeAll()
        eventChallenges.removeAll()
        
        // Reset theme to default
        applyEventTheme(.special) // Default theme
    }
    
    // MARK: - Event Participation
    func participateInEvent(activity: EventActivity) {
        guard let event = currentEvent,
              var progress = eventProgress[event.id] else { return }
        
        // Award event points based on activity
        let pointsEarned = calculateEventPoints(for: activity, in: event)
        progress.pointsEarned += pointsEarned
        
        // Update participation
        let today = Calendar.current.startOfDay(for: Date())
        if let lastParticipation = progress.lastParticipationDate,
           Calendar.current.startOfDay(for: lastParticipation) != today {
            progress.participationDays += 1
        } else if progress.lastParticipationDate == nil {
            progress.participationDays = 1
        }
        progress.lastParticipationDate = Date()
        
        eventProgress[event.id] = progress
        
        // Check for reward unlocks
        checkRewardUnlocks(for: event, progress: progress)
        
        print("🎪 Event participation: +\(pointsEarned) points (\(activity.rawValue))")
    }
    
    private func calculateEventPoints(for activity: EventActivity, in event: SeasonalEvent) -> Int {
        let basePoints = activity.basePoints
        
        // Apply event-specific multipliers
        let multiplier = event.theme.pointMultiplier(for: activity)
        
        return Int(Float(basePoints) * multiplier)
    }
    
    private func checkRewardUnlocks(for event: SeasonalEvent, progress: EventProgress) {
        for reward in event.rewards {
            let rewardId = "\(event.id)_\(reward.id)"
            
            if !unlockedRewards.contains(rewardId) && progress.pointsEarned >= reward.pointsRequired {
                unlockEventReward(reward, eventId: event.id)
            }
        }
    }
    
    private func unlockEventReward(_ reward: EventReward, eventId: String) {
        let rewardId = "\(eventId)_\(reward.id)"
        unlockedRewards.insert(rewardId)
        
        // Apply reward to player
        applyRewardToPlayer(reward)
        
        print("🎁 Event reward unlocked: \(reward.name)")
    }
    
    private func applyRewardToPlayer(_ reward: EventReward) {
        guard let player = gameManager.currentPlayer else { return }
        
        switch reward.type {
        case .coins:
            player.add(coins: Int32(reward.value))
        case .gems:
            player.add(gems: Int32(reward.value))
        case .decoration:
            // Add decoration to inventory
            break
        case .animalSkin:
            // Unlock animal skin
            break
        case .building:
            // Unlock special building
            break
        case .song:
            // Unlock special song
            break
        case .title:
            // Unlock player title
            break
        }
    }
    
    // MARK: - Challenge System
    func completeEventChallenge(_ challenge: EventChallenge) {
        guard let event = currentEvent,
              var progress = eventProgress[event.id] else { return }
        
        progress.challengesCompleted += 1
        eventProgress[event.id] = progress
        
        // Award challenge rewards
        let pointsEarned = challenge.pointsReward
        participateInEvent(activity: .challengeCompletion)
        
        print("🏆 Event challenge completed: \(challenge.title) (+\(pointsEarned) points)")
    }
    
    // MARK: - Daily Participation
    private func updateDailyParticipation() {
        guard let event = currentEvent,
              var progress = eventProgress[event.id] else { return }
        
        let today = Calendar.current.startOfDay(for: Date())
        
        if let lastParticipation = progress.lastParticipationDate,
           Calendar.current.startOfDay(for: lastParticipation) == today {
            // Already participated today
            return
        }
        
        // Award daily participation points
        participateInEvent(activity: .dailyLogin)
    }
    
    // MARK: - Data Persistence
    private func loadEventProgress() {
        // In production, load from Core Data
        // For now, use empty progress
        eventProgress = [:]
    }
    
    private func saveEventProgress() {
        // In production, save to Core Data
        // For now, just log
        print("💾 Event progress saved")
    }
    
    // MARK: - Public Interface
    func getEventTimeRemaining() -> TimeInterval? {
        return currentEvent?.endDate.timeIntervalSinceNow
    }
    
    func getEventProgress(for eventId: String) -> EventProgress? {
        return eventProgress[eventId]
    }
    
    func isRewardUnlocked(_ reward: EventReward, in event: SeasonalEvent) -> Bool {
        let rewardId = "\(event.id)_\(reward.id)"
        return unlockedRewards.contains(rewardId)
    }
    
    func getAvailableEventActivities() -> [EventActivity] {
        guard currentEvent != nil else { return [] }
        return EventActivity.allCases
    }
}

// MARK: - Event Models
struct SeasonalEvent: Identifiable {
    let id: String
    let name: String
    let description: String
    let type: EventType
    let theme: EventTheme
    let startDate: Date
    let endDate: Date
    let rewards: [EventReward]
    let challenges: [EventChallenge]
    let specialFeatures: [String]
    
    var isActive: Bool {
        let now = Date()
        return now >= startDate && now <= endDate
    }
    
    var timeRemaining: TimeInterval {
        return endDate.timeIntervalSinceNow
    }
    
    var daysRemaining: Int {
        let days = Calendar.current.dateComponents([.day], from: Date(), to: endDate).day ?? 0
        return max(0, days)
    }
}

enum EventType: String, CaseIterable {
    case seasonal = "seasonal"
    case holiday = "holiday"
    case special = "special"
    case community = "community"
    
    var displayName: String {
        switch self {
        case .seasonal: return "Seasonal"
        case .holiday: return "Holiday"
        case .special: return "Special"
        case .community: return "Community"
        }
    }
}

enum EventTheme: String, CaseIterable {
    case spring = "spring"
    case summer = "summer"
    case halloween = "halloween"
    case winter = "winter"
    case special = "special"
    
    var displayName: String {
        switch self {
        case .spring: return "Spring"
        case .summer: return "Summer"
        case .halloween: return "Halloween"
        case .winter: return "Winter"
        case .special: return "Special"
        }
    }
    
    var primaryColor: String {
        switch self {
        case .spring: return "#90EE90"  // Light Green
        case .summer: return "#FFD700"  // Gold
        case .halloween: return "#FF4500" // Orange Red
        case .winter: return "#87CEEB"  // Sky Blue
        case .special: return "#9370DB" // Medium Purple
        }
    }
    
    func pointMultiplier(for activity: EventActivity) -> Float {
        switch (self, activity) {
        case (.spring, .farmBuilding), (.spring, .animalCare):
            return 1.5
        case (.summer, .rhythmGame), (.summer, .socialInteraction):
            return 1.5
        case (.halloween, .challengeCompletion), (.halloween, .specialEvent):
            return 2.0
        case (.winter, .dailyLogin), (.winter, .giftExchange):
            return 1.3
        default:
            return 1.0
        }
    }
}

enum EventActivity: String, CaseIterable {
    case rhythmGame = "rhythm_game"
    case farmBuilding = "farm_building"
    case animalCare = "animal_care"
    case socialInteraction = "social_interaction"
    case challengeCompletion = "challenge_completion"
    case dailyLogin = "daily_login"
    case giftExchange = "gift_exchange"
    case specialEvent = "special_event"
    
    var displayName: String {
        switch self {
        case .rhythmGame: return "Play Rhythm Game"
        case .farmBuilding: return "Build on Farm"
        case .animalCare: return "Care for Animals"
        case .socialInteraction: return "Social Interaction"
        case .challengeCompletion: return "Complete Challenge"
        case .dailyLogin: return "Daily Login"
        case .giftExchange: return "Exchange Gifts"
        case .specialEvent: return "Special Event"
        }
    }
    
    var basePoints: Int {
        switch self {
        case .rhythmGame: return 50
        case .farmBuilding: return 30
        case .animalCare: return 20
        case .socialInteraction: return 40
        case .challengeCompletion: return 100
        case .dailyLogin: return 10
        case .giftExchange: return 25
        case .specialEvent: return 75
        }
    }
}

struct EventProgress {
    let eventId: String
    var pointsEarned: Int
    var challengesCompleted: Int
    var rewardsUnlocked: [String]
    var participationDays: Int
    var lastParticipationDate: Date?
}

struct EventReward: Identifiable {
    let id: String
    let name: String
    let description: String
    let type: EventRewardType
    let value: Int
    let pointsRequired: Int
    let isExclusive: Bool
    let rarity: RewardRarity
}

enum EventRewardType: String, CaseIterable {
    case coins = "coins"
    case gems = "gems"
    case decoration = "decoration"
    case animalSkin = "animal_skin"
    case building = "building"
    case song = "song"
    case title = "title"
    
    var displayName: String {
        switch self {
        case .coins: return "Coins"
        case .gems: return "Gems"
        case .decoration: return "Decoration"
        case .animalSkin: return "Animal Skin"
        case .building: return "Building"
        case .song: return "Song"
        case .title: return "Title"
        }
    }
    
    var icon: String {
        switch self {
        case .coins: return "🪙"
        case .gems: return "💎"
        case .decoration: return "🌸"
        case .animalSkin: return "🎨"
        case .building: return "🏗️"
        case .song: return "🎵"
        case .title: return "🏆"
        }
    }
}

enum RewardRarity: String, CaseIterable {
    case common = "common"
    case rare = "rare"
    case epic = "epic"
    case legendary = "legendary"
    case exclusive = "exclusive"
    
    var displayName: String {
        switch self {
        case .common: return "Common"
        case .rare: return "Rare"
        case .epic: return "Epic"
        case .legendary: return "Legendary"
        case .exclusive: return "Exclusive"
        }
    }
    
    var color: String {
        switch self {
        case .common: return "#808080"    // Gray
        case .rare: return "#0080FF"     // Blue
        case .epic: return "#8000FF"     // Purple
        case .legendary: return "#FFD700" // Gold
        case .exclusive: return "#FF1493" // Deep Pink
        }
    }
}

struct EventChallenge: Identifiable {
    let id: String
    let title: String
    let description: String
    let type: EventChallengeType
    let targetValue: Int
    var currentProgress: Int
    let pointsReward: Int
    let specialReward: EventReward?
    let expiresAt: Date
    
    var isCompleted: Bool {
        return currentProgress >= targetValue
    }
    
    var progressPercentage: Float {
        return Float(currentProgress) / Float(targetValue)
    }
}

enum EventChallengeType: String, CaseIterable {
    case rhythmScore = "rhythm_score"
    case farmLevel = "farm_level"
    case animalCollection = "animal_collection"
    case socialActivity = "social_activity"
    case dailyStreak = "daily_streak"
    case specialTask = "special_task"
    
    var displayName: String {
        switch self {
        case .rhythmScore: return "Rhythm Score"
        case .farmLevel: return "Farm Level"
        case .animalCollection: return "Animal Collection"
        case .socialActivity: return "Social Activity"
        case .dailyStreak: return "Daily Streak"
        case .specialTask: return "Special Task"
        }
    }
}
