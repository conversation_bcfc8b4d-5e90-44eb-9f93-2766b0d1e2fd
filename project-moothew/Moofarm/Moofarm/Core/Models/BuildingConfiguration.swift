//
//  BuildingConfiguration.swift
//  Moofarm
//
//  Created by Project Moothew on 28/7/25.
//

import Foundation
import SwiftUI

/// Building configuration and types
struct BuildingConfiguration: Codable {
    let type: BuildingType
    let name: String
    let description: String
    let size: CGSize
    let baseCost: Int
    let upgradeCosts: [Int]
    let maxLevel: Int
    let productionRate: Float
    let unlockLevel: Int
    
    enum BuildingType: String, CaseIterable, Codable {
        case barn = "barn"
        case house = "house"
        case coop = "coop"
        case pond = "pond"
        case musicHall = "music_hall"
        case practiceRoom = "practice_room"
        case decoration = "decoration"
        
        var displayName: String {
            switch self {
            case .barn: return "Barn"
            case .house: return "Farm House"
            case .coop: return "Chicken Coop"
            case .pond: return "Duck Pond"
            case .musicHall: return "Music Hall"
            case .practiceRoom: return "Practice Room"
            case .decoration: return "Decoration"
            }
        }
        
        var emoji: String {
            switch self {
            case .barn: return "🏚️"
            case .house: return "🏡"
            case .coop: return "🐔"
            case .pond: return "🦆"
            case .musicHall: return "🎵"
            case .practiceRoom: return "🎼"
            case .decoration: return "🌸"
            }
        }
    }
    
    static let defaultBuildings: [BuildingConfiguration] = [
        BuildingConfiguration(
            type: .barn,
            name: "Barn",
            description: "Houses farm animals",
            size: CGSize(width: 2, height: 2),
            baseCost: 500,
            upgradeCosts: [750, 1000, 1500],
            maxLevel: 4,
            productionRate: 1.0,
            unlockLevel: 1
        ),
        BuildingConfiguration(
            type: .house,
            name: "Farm House",
            description: "Your cozy home",
            size: CGSize(width: 3, height: 3),
            baseCost: 1000,
            upgradeCosts: [1500, 2000, 3000],
            maxLevel: 4,
            productionRate: 0.5,
            unlockLevel: 1
        ),
        BuildingConfiguration(
            type: .coop,
            name: "Chicken Coop",
            description: "Houses chickens and ducks",
            size: CGSize(width: 2, height: 1),
            baseCost: 300,
            upgradeCosts: [450, 600, 900],
            maxLevel: 4,
            productionRate: 1.5,
            unlockLevel: 2
        ),
        BuildingConfiguration(
            type: .pond,
            name: "Duck Pond",
            description: "A peaceful water feature",
            size: CGSize(width: 2, height: 2),
            baseCost: 800,
            upgradeCosts: [1200, 1600, 2400],
            maxLevel: 4,
            productionRate: 0.8,
            unlockLevel: 3
        ),
        BuildingConfiguration(
            type: .musicHall,
            name: "Music Hall",
            description: "Boosts rhythm game performance",
            size: CGSize(width: 3, height: 2),
            baseCost: 2000,
            upgradeCosts: [3000, 4000, 6000],
            maxLevel: 4,
            productionRate: 0.0,
            unlockLevel: 5
        ),
        BuildingConfiguration(
            type: .practiceRoom,
            name: "Practice Room",
            description: "Improves rhythm skills",
            size: CGSize(width: 2, height: 2),
            baseCost: 1500,
            upgradeCosts: [2250, 3000, 4500],
            maxLevel: 4,
            productionRate: 0.0,
            unlockLevel: 4
        ),
        BuildingConfiguration(
            type: .decoration,
            name: "Decoration",
            description: "Beautifies your farm",
            size: CGSize(width: 1, height: 1),
            baseCost: 100,
            upgradeCosts: [150, 200, 300],
            maxLevel: 4,
            productionRate: 0.0,
            unlockLevel: 2
        )
    ]
}
