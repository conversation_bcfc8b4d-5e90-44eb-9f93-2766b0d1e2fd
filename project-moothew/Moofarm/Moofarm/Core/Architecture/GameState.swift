//
//  GameState.swift
//  Moofarm
//
//  Created by Project Moothew on 28/7/25.
//

import Foundation

// MARK: - Game State Management

/// Main game states for navigation and flow control
enum GameState: String, CaseIterable {
    case menu = "menu"
    case rhythmGame = "rhythmGame"
    case farm = "farm"
    case settings = "settings"
    case loading = "loading"
    case animalCollection = "animalCollection"
    case battlePass = "battlePass"
    
    var displayName: String {
        switch self {
        case .menu:
            return "Main Menu"
        case .rhythmGame:
            return "Rhythm Game"
        case .farm:
            return "Farm"
        case .settings:
            return "Settings"
        case .loading:
            return "Loading"
        case .animalCollection:
            return "Animal Collection"
        case .battlePass:
            return "Battle Pass"
        }
    }
}

/// Rhythm game specific states
enum RhythmGameState: String, CaseIterable {
    case idle = "idle"
    case playing = "playing"
    case paused = "paused"
    case completed = "completed"
    case failed = "failed"
    
    var canTransitionTo: [RhythmGameState] {
        switch self {
        case .idle:
            return [.playing]
        case .playing:
            return [.paused, .completed, .failed]
        case .paused:
            return [.playing, .idle]
        case .completed, .failed:
            return [.idle]
        }
    }
}

/// Farm building states
enum FarmState: String, CaseIterable {
    case viewing = "viewing"
    case building = "building"
    case editing = "editing"
    case decorating = "decorating"
    
    var displayName: String {
        switch self {
        case .viewing:
            return "Viewing Farm"
        case .building:
            return "Building Mode"
        case .editing:
            return "Edit Mode"
        case .decorating:
            return "Decorating"
        }
    }
}

// MARK: - Player Progress States

/// Overall player progression levels
enum PlayerLevel: Int, CaseIterable {
    case beginner = 1
    case novice = 5
    case intermediate = 10
    case advanced = 20
    case expert = 35
    case master = 50
    
    var displayName: String {
        switch self {
        case .beginner:
            return "Beginner Farmer"
        case .novice:
            return "Novice Farmer"
        case .intermediate:
            return "Intermediate Farmer"
        case .advanced:
            return "Advanced Farmer"
        case .expert:
            return "Expert Farmer"
        case .master:
            return "Master Farmer"
        }
    }
    
    var requiredExperience: Int {
        switch self {
        case .beginner:
            return 0
        case .novice:
            return 500
        case .intermediate:
            return 1500
        case .advanced:
            return 5000
        case .expert:
            return 15000
        case .master:
            return 50000
        }
    }
    
    static func level(for experience: Int) -> PlayerLevel {
        let sortedLevels = PlayerLevel.allCases.sorted { $0.requiredExperience > $1.requiredExperience }
        return sortedLevels.first { experience >= $0.requiredExperience } ?? .beginner
    }
}

// MARK: - Game Difficulty

/// Difficulty levels for rhythm game
enum Difficulty: String, CaseIterable {
    case easy = "easy"
    case normal = "normal"
    case hard = "hard"
    
    var displayName: String {
        switch self {
        case .easy:
            return "Easy"
        case .normal:
            return "Normal"
        case .hard:
            return "Hard"
        }
    }
    
    var scoreMultiplier: Float {
        switch self {
        case .easy:
            return 1.0
        case .normal:
            return 1.5
        case .hard:
            return 2.0
        }
    }
    
    var noteSpeed: Float {
        switch self {
        case .easy:
            return 1.0
        case .normal:
            return 1.3
        case .hard:
            return 1.6
        }
    }
}

// MARK: - Hit Accuracy

/// Rhythm game hit accuracy levels
enum HitAccuracy: String, CaseIterable {
    case perfect = "perfect"
    case good = "good"
    case okay = "okay"
    case miss = "miss"
    
    var displayName: String {
        switch self {
        case .perfect:
            return "Perfect!"
        case .good:
            return "Good"
        case .okay:
            return "Okay"
        case .miss:
            return "Miss"
        }
    }
    
    var baseScore: Int {
        switch self {
        case .perfect:
            return 100
        case .good:
            return 75
        case .okay:
            return 50
        case .miss:
            return 0
        }
    }
    
    var timingTolerance: TimeInterval {
        switch self {
        case .perfect:
            return 0.05  // 50ms
        case .good:
            return 0.10  // 100ms
        case .okay:
            return 0.15  // 150ms
        case .miss:
            return Double.infinity
        }
    }
}
