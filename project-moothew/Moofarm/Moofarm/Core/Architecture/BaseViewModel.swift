//
//  BaseViewModel.swift
//  Moofarm
//
//  Created by Project Moothew on 28/7/25.
//

import Foundation
import Combine
import SwiftUI

/// Base class for all ViewModels in the app
/// Provides common functionality like loading states, error handling, and Combine support
class BaseViewModel: ObservableObject {
    
    // MARK: - Published Properties
    @Published var isLoading: Bool = false
    @Published var errorMessage: String?
    @Published var showError: Bool = false
    
    // MARK: - Combine
    var cancellables = Set<AnyCancellable>()
    
    // MARK: - Initialization
    init() {
        setupBindings()
    }
    
    // MARK: - Setup
    private func setupBindings() {
        // Auto-show error when errorMessage is set
        $errorMessage
            .map { $0 != nil }
            .assign(to: \.showError, on: self)
            .store(in: &cancellables)
    }
    
    // MARK: - Error Handling
    func handleError(_ error: Error) {
        DispatchQueue.main.async {
            self.isLoading = false
            self.errorMessage = error.localizedDescription
        }
    }
    
    func clearError() {
        errorMessage = nil
        showError = false
    }
    
    // MARK: - Loading State
    func setLoading(_ loading: Bool) {
        DispatchQueue.main.async {
            self.isLoading = loading
        }
    }
    
    // MARK: - Cleanup
    deinit {
        cancellables.removeAll()
    }
}

// MARK: - Loading State Extension
extension BaseViewModel {
    
    /// Execute an async operation with automatic loading state management
    func executeWithLoading<T>(_ operation: @escaping () async throws -> T) async -> T? {
        setLoading(true)
        defer { setLoading(false) }
        
        do {
            return try await operation()
        } catch {
            handleError(error)
            return nil
        }
    }
    
    /// Execute a Combine publisher with automatic loading state management
    func executeWithLoading<T, E: Error>(_ publisher: AnyPublisher<T, E>) -> AnyPublisher<T?, Never> {
        setLoading(true)
        
        return publisher
            .map { Optional($0) }
            .catch { [weak self] error -> AnyPublisher<T?, Never> in
                self?.handleError(error)
                return Just(nil).eraseToAnyPublisher()
            }
            .handleEvents(receiveCompletion: { [weak self] _ in
                self?.setLoading(false)
            })
            .eraseToAnyPublisher()
    }
}
