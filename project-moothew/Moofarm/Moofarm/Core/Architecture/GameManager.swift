//
//  GameManager.swift
//  Moofarm
//
//  Created by Project Moothew on 28/7/25.
//

import Foundation
import Combine
import CoreData

/// Central game manager that coordinates all game systems
/// Handles player progression, cross-system integration, and game state
class GameManager: ObservableObject {
    
    // MARK: - Singleton
    static let shared = GameManager()
    
    // MARK: - Published Properties
    @Published var currentPlayer: Player?
    @Published var gameState: GameState = .loading
    @Published var isInitialized: Bool = false
    
    // MARK: - Services
    private let dataService: DataService
    private let audioService: AudioService
    private let gameKitService: GameKitService
    private let coreDataStack: CoreDataStack

    // MARK: - Advanced Systems
    private let battlePassSystem: BattlePassSystem
    private let socialSystem: SocialSystem
    private let seasonalEventSystem: SeasonalEventSystem
    private let achievementSystem: AdvancedAchievementSystem
    
    // MARK: - Combine
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Initialization
    init(
        dataService: DataService = DataService.shared,
        audioService: AudioService = AudioService.shared,
        gameKitService: GameKitService = GameKitService.shared,
        coreDataStack: CoreDataStack = CoreDataStack.shared
    ) {
        self.dataService = dataService
        self.audioService = audioService
        self.gameKitService = gameKitService
        self.coreDataStack = coreDataStack

        // Initialize advanced systems with placeholder values first
        self.battlePassSystem = BattlePassSystem(dataService: dataService)
        self.socialSystem = SocialSystem(gameKitService: gameKitService, dataService: dataService)
        self.seasonalEventSystem = SeasonalEventSystem(dataService: dataService)
        self.achievementSystem = AdvancedAchievementSystem(dataService: dataService, gameKitService: gameKitService)

        setupBindings()
    }
    
    // MARK: - Setup
    private func setupBindings() {
        // Listen for GameKit authentication changes
        gameKitService.$isAuthenticated
            .sink { [weak self] isAuthenticated in
                if isAuthenticated {
                    self?.syncWithGameCenter()
                }
            }
            .store(in: &cancellables)
    }
    
    // MARK: - Game Initialization
    
    /// Initialize the game system
    func initializeGame() async {
        gameState = .loading

        do {
            // Load or create player
            await loadOrCreatePlayer()

            // Initialize default data if needed
            await initializeDefaultData()

            // Setup game systems
            await setupGameSystems()

            await MainActor.run {
                isInitialized = true
                gameState = .menu
                print("🎮 Game Manager initialized successfully")
            }

        } catch {
            print("❌ Game initialization failed: \(error)")
            print("❌ Error details: \(error.localizedDescription)")
            await MainActor.run {
                isInitialized = true // Mark as initialized even if failed
                gameState = .menu // Fallback to menu even if initialization fails
                print("🎮 Game Manager fallback initialization complete")
            }
        }
    }
    
    @MainActor
    private func loadOrCreatePlayer() async {
        do {
            let request = Player.fetchRequest()
            request.fetchLimit = 1

            let players = try dataService.viewContext.fetch(request)

            if let existingPlayer = players.first {
                currentPlayer = existingPlayer
                existingPlayer.startSession()
                print("✅ Loaded existing player: \(existingPlayer.playerName ?? "Unknown")")
            } else {
                // Create new player
                let newPlayer = Player(context: dataService.viewContext)
                newPlayer.playerName = "New Farmer"
                newPlayer.createdAt = Date()
                newPlayer.lastPlayedAt = Date()
                newPlayer.experience = 0
                newPlayer.coins = 1000 // Starting coins
                newPlayer.gems = 50 // Starting gems

                currentPlayer = newPlayer

                try dataService.viewContext.save()
                print("✅ Created new player")
            }
        } catch {
            print("❌ Failed to load/create player: \(error)")
            print("❌ Core Data error details: \(error.localizedDescription)")

            // Create a temporary player to avoid crashes
            let tempPlayer = Player(context: dataService.viewContext)
            tempPlayer.playerName = "Temp Player"
            tempPlayer.createdAt = Date()
            tempPlayer.lastPlayedAt = Date()
            tempPlayer.experience = 0
            tempPlayer.coins = 1000
            tempPlayer.gems = 50
            currentPlayer = tempPlayer

            print("⚠️ Created temporary player due to Core Data issues")
        }
    }
    
    private func initializeDefaultData() async {
        guard let player = currentPlayer else { return }
        
        do {
            // Create default animals if none exist
            let animalCount = try dataService.viewContext.count(for: Animal.fetchRequest())
            if animalCount == 0 {
                await createDefaultAnimals()
            }
            
            // Create default songs if none exist
            let songCount = try dataService.viewContext.count(for: Song.fetchRequest())
            if songCount == 0 {
                await createDefaultSongs()
            }
            
            // Create farm if doesn't exist
            if player.farm == nil {
                await createDefaultFarm(for: player)
            }
            
            try await MainActor.run {
                try dataService.viewContext.save()
            }
            
        } catch {
            print("❌ Failed to initialize default data: \(error)")
        }
    }
    
    @MainActor
    private func createDefaultAnimals() async {
        let defaultAnimals: [(AnimalType, AnimalRarity, Bool)] = [
            (.cow, .common, true),      // Starting animal
            (.pig, .common, false),
            (.chicken, .common, false),
            (.sheep, .rare, false),
            (.duck, .rare, false),
            (.goat, .epic, false)
        ]
        
        for (type, rarity, unlocked) in defaultAnimals {
            let animal = Animal(context: dataService.viewContext)
            animal.type = type.rawValue
            animal.level = 1
            animal.happiness = 0.5
            animal.isUnlocked = unlocked
            animal.rarity = rarity.rawValue
            animal.player = currentPlayer
        }
        
        print("✅ Created default animals")
    }
    
    @MainActor
    private func createDefaultSongs() async {
        for songConfig in SongConfiguration.defaultSongs {
            let song = Song(context: dataService.viewContext)
            song.title = songConfig.title
            song.difficulty = songConfig.difficulty.rawValue
            song.bpm = songConfig.bpm
            song.duration = songConfig.duration
            song.fileName = "\(songConfig.title.lowercased().replacingOccurrences(of: " ", with: "_")).mp3"
            song.isUnlocked = songConfig.difficulty == .easy // Unlock easy songs by default
            
            if song.isUnlocked {
                song.unlockedAt = Date()
            }
        }
        
        print("✅ Created default songs")
    }
    
    @MainActor
    private func createDefaultFarm(for player: Player) async {
        let farm = Farm(context: dataService.viewContext)
        farm.farmName = "\(player.playerName ?? "Player")'s Farm"
        farm.level = 1
        farm.happiness = 0.7
        farm.lastUpdated = Date()
        farm.totalBuildings = 0
        farm.farmRating = 0
        farm.player = player
        
        // Create empty layout
        let layout = FarmLayout.empty
        if let layoutData = try? JSONEncoder().encode(layout) {
            farm.layoutData = layoutData
        }
        
        print("✅ Created default farm")
    }
    
    private func setupGameSystems() async {
        // Setup audio system (with error handling)
        _ = audioService.playMusic("menu_background", loop: true)
        print("✅ Background music setup initiated")

        // Authenticate with GameKit (with error handling)
        _ = gameKitService.authenticatePlayer()
        print("✅ GameKit authentication initiated")

        print("✅ Game systems setup complete")
    }
    
    // MARK: - Game Flow Management
    
    /// Handle rhythm game completion with farm integration
    func handleRhythmGameComplete(scoreData: ScoreData, song: Song) async {
        guard let player = currentPlayer else { return }

        await MainActor.run {
            // Apply farm happiness bonus to rewards
            let farmBonus = getFarmRhythmBonus()
            let bonusCoins = Int32(Float(scoreData.coinsEarned) * farmBonus)
            let bonusXP = Int32(Float(scoreData.experienceEarned) * farmBonus)

            // Update player stats with farm bonus
            player.add(
                coins: bonusCoins,
                experience: bonusXP
            )
            player.addBattlePassXP(bonusXP)

            // Update or create song progress
            updateSongProgress(for: song, scoreData: scoreData, player: player)

            // Apply rhythm results to farm
            applyRhythmResultsToFarm(scoreData: scoreData)

            // Check for animal unlocks
            checkAnimalUnlocks(basedOn: scoreData)

            // Check achievements
            checkAchievements(for: player)

            // Update Battle Pass progress
            battlePassSystem.addXP(Int(bonusXP), source: .rhythmGame)

            // Update seasonal event progress
            seasonalEventSystem.participateInEvent(activity: .rhythmGame)

            // Save changes
            do {
                try dataService.viewContext.save()
                print("✅ Rhythm game completion processed with farm integration")
                print("🏡 Farm bonus applied: \(farmBonus)x")
            } catch {
                print("❌ Failed to save rhythm game completion: \(error)")
            }
        }

        // Submit score to GameKit
        if gameKitService.isAuthenticated {
            _ = gameKitService.submitRhythmScore(scoreData.totalScore)
        }
    }
    
    @MainActor
    private func updateSongProgress(for song: Song, scoreData: ScoreData, player: Player) {
        // Find existing progress or create new
        let existingProgress = player.songProgress?.first { progress in
            (progress as? SongProgress)?.song == song
        } as? SongProgress
        
        let progress = existingProgress ?? {
            let newProgress = SongProgress(context: dataService.viewContext)
            newProgress.player = player
            newProgress.song = song
            return newProgress
        }()
        
        // Update progress
        progress.timesPlayed += 1
        progress.lastPlayedAt = Date()
        
        if scoreData.totalScore > progress.highScore {
            progress.highScore = Int32(scoreData.totalScore)
        }
        
        if scoreData.combo > progress.bestCombo {
            progress.bestCombo = Int32(scoreData.combo)
        }
        
        progress.perfectHits += Int32(scoreData.perfectHits)
        progress.goodHits += Int32(scoreData.goodHits)
        progress.okayHits += Int32(scoreData.okayHits)
        progress.missedHits += Int32(scoreData.missedHits)
        
        // Mark as completed if score is high enough
        if scoreData.accuracyPercentage >= 70.0 && !progress.isCompleted {
            progress.isCompleted = true
            progress.completedAt = Date()
        }
    }
    
    @MainActor
    private func checkAnimalUnlocks(basedOn scoreData: ScoreData) {
        // Simple unlock logic based on score
        let unlockThreshold = 50000
        
        if scoreData.totalScore >= unlockThreshold {
            // Try to unlock a random locked animal
            let request = Animal.fetchRequest()
            request.predicate = NSPredicate(format: "isUnlocked == NO")
            request.fetchLimit = 1
            
            do {
                let lockedAnimals = try dataService.viewContext.fetch(request)
                if let animalToUnlock = lockedAnimals.first {
                    animalToUnlock.unlock()
                    print("🎉 Unlocked new animal: \(animalToUnlock.displayName)")
                    
                    // Play unlock sound
                    _ = audioService.playUISound(.success)
                }
            } catch {
                print("❌ Failed to check animal unlocks: \(error)")
            }
        }
    }
    
    @MainActor
    private func checkAchievements(for player: Player) {
        // Check advanced achievements
        let newAchievements = achievementSystem.checkAchievements(for: player)

        for achievementId in newAchievements {
            // Report to GameKit
            if gameKitService.isAuthenticated {
                switch achievementId {
                case "first_song":
                    _ = gameKitService.reportFirstSongAchievement()
                case "animal_collector":
                    _ = gameKitService.reportAnimalCollectorAchievement(animalCount: Int(player.totalAnimalsUnlocked))
                case "perfect_combo":
                    let maxCombo = player.songProgress?.allObjects.compactMap { ($0 as? SongProgress)?.bestCombo }.max() ?? 0
                    _ = gameKitService.reportPerfectComboAchievement(comboCount: Int(maxCombo))
                default:
                    break
                }
            }

            // Award Battle Pass XP for achievements
            battlePassSystem.addXP(50, source: .achievement)
        }
    }
    
    // MARK: - Farm Management

    /// Handle farm building placement
    func placeBuildingOnFarm(buildingType: BuildingConfiguration.BuildingType, at position: GridPosition) async -> Bool {
        guard let player = currentPlayer,
              let farm = player.farm else { return false }

        // Check if player can afford the building
        let config = BuildingConfiguration.defaultBuildings.first { $0.type == buildingType }
        guard let buildingConfig = config,
              player.canAfford(coins: Int32(buildingConfig.baseCost)) else {
            return false
        }

        return await MainActor.run {
            // Spend coins
            player.spend(coins: Int32(buildingConfig.baseCost))

            // Create building
            let building = Building(context: dataService.viewContext)
            building.type = buildingType.rawValue
            building.level = 1
            building.positionX = Int32(position.x)
            building.positionY = Int32(position.y)
            building.isConstructed = true
            building.constructedAt = Date()
            building.productionRate = buildingConfig.productionRate
            building.farm = farm

            // Update farm stats
            farm.totalBuildings += 1
            farm.lastUpdated = Date()

            // Check for farm level up
            checkFarmLevelUp(farm: farm)

            // Save changes
            do {
                try dataService.viewContext.save()
                print("✅ Building placed: \(buildingConfig.name)")
                return true
            } catch {
                print("❌ Failed to place building: \(error)")
                return false
            }
        }
    }

    // MARK: - Farm-Rhythm Integration

    /// Get farm happiness bonus for rhythm game
    private func getFarmRhythmBonus() -> Float {
        guard let farm = currentPlayer?.farm else { return 1.0 }

        // Base bonus from farm happiness (0.75x to 1.25x)
        let happinessBonus = 0.75 + (farm.happiness * 0.5)

        // Additional bonus from farm level
        let levelBonus = 1.0 + (Float(farm.level - 1) * 0.05) // 5% per level above 1

        // Music buildings provide extra bonus
        let musicBuildingBonus = getMusicBuildingBonus(farm: farm)

        return happinessBonus * levelBonus * musicBuildingBonus
    }

    /// Apply rhythm game results to farm
    private func applyRhythmResultsToFarm(scoreData: ScoreData) {
        guard let farm = currentPlayer?.farm else { return }

        // Calculate happiness bonus based on performance
        let performanceRatio = scoreData.accuracyPercentage / 100.0
        let happinessBonus = Float(performanceRatio * 0.1) // Up to 10% happiness boost

        // Apply happiness bonus
        let oldHappiness = farm.happiness
        farm.happiness = min(1.0, farm.happiness + happinessBonus)

        // Update farm rating based on score
        let ratingIncrease = Int32(scoreData.totalScore / 1000) // 1 rating per 1000 points
        farm.farmRating += ratingIncrease

        // Update last updated time
        farm.lastUpdated = Date()

        print("🏡 Farm updated - Happiness: \(oldHappiness) → \(farm.happiness), Rating: +\(ratingIncrease)")

        // Check for farm level up
        checkFarmLevelUp(farm: farm)
    }

    /// Check if farm should level up
    private func checkFarmLevelUp(farm: Farm) {
        let requiredBuildings = Int32(farm.level * 3) // 3 buildings per level
        let requiredHappiness: Float = 0.6 + (Float(farm.level) * 0.05) // Increasing happiness requirement
        let requiredRating = farm.level * 100 // 100 rating per level

        if farm.totalBuildings >= requiredBuildings &&
           farm.happiness >= requiredHappiness &&
           farm.farmRating >= requiredRating {

            let oldLevel = farm.level
            farm.level += 1
            farm.farmRating += 100 // Bonus rating for level up

            // Unlock new animals based on farm level
            unlockFarmLevelAnimals(level: farm.level)

            print("🎉 Farm leveled up: \(oldLevel) → \(farm.level)!")

            // Play level up sound
            _ = audioService.playUISound(.levelUp)
        }
    }

    /// Get bonus from music-related buildings
    private func getMusicBuildingBonus(farm: Farm) -> Float {
        guard let buildings = farm.buildings?.allObjects as? [Building] else { return 1.0 }

        var bonus: Float = 1.0

        for building in buildings {
            switch building.type {
            case "music_hall":
                bonus += 0.1 * Float(building.level) // 10% per level
            case "practice_room":
                bonus += 0.05 * Float(building.level) // 5% per level
            default:
                break
            }
        }

        return bonus
    }

    /// Unlock animals based on farm level
    private func unlockFarmLevelAnimals(level: Int32) {
        let animalUnlocks: [Int32: [AnimalType]] = [
            2: [.pig, .chicken],
            3: [.sheep, .duck],
            4: [.goat, .horse],
            5: [.rabbit, .cat],
            6: [.dog, .turkey],
            7: [.llama]
        ]

        guard let animalsToUnlock = animalUnlocks[level] else { return }

        for animalType in animalsToUnlock {
            // Find locked animal of this type
            let request = Animal.fetchRequest()
            request.predicate = NSPredicate(format: "type == %@ AND isUnlocked == NO", animalType.rawValue)
            request.fetchLimit = 1

            do {
                let lockedAnimals = try dataService.viewContext.fetch(request)
                if let animal = lockedAnimals.first {
                    animal.unlock()
                    print("🐾 Unlocked \(animalType.displayName) at farm level \(level)!")
                }
            } catch {
                print("❌ Failed to unlock animals: \(error)")
            }
        }
    }
    
    // MARK: - GameKit Integration
    
    private func syncWithGameCenter() {
        guard let player = currentPlayer else { return }
        
        // Submit current stats to GameKit
        _ = gameKitService.submitRhythmScore(Int(player.songProgress?.allObjects.compactMap { ($0 as? SongProgress)?.highScore }.max() ?? 0))
        _ = gameKitService.submitFarmRating(Int(player.farm?.farmRating ?? 0))
        
        print("✅ Synced with Game Center")
    }
    
    // MARK: - Session Management
    
    /// End current game session
    func endSession() {
        guard let player = currentPlayer else { return }

        let sessionDuration = Date().timeIntervalSince(player.lastPlayedAt ?? Date())
        player.endSession(duration: sessionDuration)

        do {
            try dataService.viewContext.save()
            print("✅ Game session ended")
        } catch {
            print("❌ Failed to save session end: \(error)")
        }
    }

    // MARK: - Advanced Systems Access

    /// Get Battle Pass system
    func getBattlePassSystem() -> BattlePassSystem {
        return battlePassSystem
    }

    /// Get Social system
    func getSocialSystem() -> SocialSystem {
        return socialSystem
    }

    /// Get Seasonal Event system
    func getSeasonalEventSystem() -> SeasonalEventSystem {
        return seasonalEventSystem
    }

    /// Get Achievement system
    func getAchievementSystem() -> AdvancedAchievementSystem {
        return achievementSystem
    }

    // MARK: - Cross-System Integration

    /// Handle farm building placement with advanced system integration
    func placeBuildingOnFarmWithIntegration(buildingType: BuildingConfiguration.BuildingType, at position: GridPosition) async -> Bool {
        let success = await placeBuildingOnFarm(buildingType: buildingType, at: position)

        if success {
            // Update Battle Pass
            battlePassSystem.addXP(30, source: .farmBuilding)

            // Update seasonal events
            seasonalEventSystem.participateInEvent(activity: .farmBuilding)

            // Check achievements
            if let player = currentPlayer {
                _ = achievementSystem.checkAchievements(for: player)
            }
        }

        return success
    }

    /// Handle daily login with advanced system integration
    func handleDailyLogin() {
        guard let player = currentPlayer else { return }

        // Update consecutive days
        let today = Calendar.current.startOfDay(for: Date())
        let lastLogin = Calendar.current.startOfDay(for: player.lastPlayedAt ?? Date())

        // Note: consecutiveDays property would need to be added to Core Data model
        // if Calendar.current.dateComponents([.day], from: lastLogin, to: today).day == 1 {
        //     player.consecutiveDays += 1
        // } else if Calendar.current.dateComponents([.day], from: lastLogin, to: today).day ?? 0 > 1 {
        //     player.consecutiveDays = 1
        // }

        player.lastPlayedAt = Date()

        // Update Battle Pass
        battlePassSystem.addXP(10, source: .dailyLogin)

        // Update seasonal events
        seasonalEventSystem.participateInEvent(activity: .dailyLogin)

        // Check achievements
        _ = achievementSystem.checkAchievements(for: player)

        print("📅 Daily login processed")
    }

    /// Handle social interaction
    func handleSocialInteraction(type: SocialInteractionType) {
        // Update Battle Pass
        let xpAmount = type.xpReward
        battlePassSystem.addXP(xpAmount, source: .social)

        // Update seasonal events
        seasonalEventSystem.participateInEvent(activity: .socialInteraction)

        print("👥 Social interaction: \(type.rawValue) (+\(xpAmount) XP)")
    }
}

// MARK: - Social Interaction Types
enum SocialInteractionType: String, CaseIterable {
    case farmVisit = "farm_visit"
    case friendHelp = "friend_help"
    case giftSent = "gift_sent"
    case giftReceived = "gift_received"

    var xpReward: Int {
        switch self {
        case .farmVisit: return 25
        case .friendHelp: return 30
        case .giftSent: return 15
        case .giftReceived: return 10
        }
    }
}
