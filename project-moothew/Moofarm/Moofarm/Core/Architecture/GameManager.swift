//
//  GameManager.swift
//  Moofarm
//
//  Created by Project Moothew on 28/7/25.
//

import Foundation
import Combine
import CoreData

/// Central game manager that coordinates all game systems
/// Handles player progression, cross-system integration, and game state
class GameManager: ObservableObject {
    
    // MARK: - Singleton
    static let shared = GameManager()
    
    // MARK: - Published Properties
    @Published var currentPlayer: Player?
    @Published var gameState: GameState = .loading
    @Published var isInitialized: Bool = false
    
    // MARK: - Services
    private let dataService: DataService
    private let audioService: AudioService
    private let gameKitService: GameKitService
    private let coreDataStack: CoreDataStack
    
    // MARK: - Combine
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Initialization
    init(
        dataService: DataService = DataService.shared,
        audioService: AudioService = AudioService.shared,
        gameKitService: GameKitService = GameKitService.shared,
        coreDataStack: CoreDataStack = CoreDataStack.shared
    ) {
        self.dataService = dataService
        self.audioService = audioService
        self.gameKitService = gameKitService
        self.coreDataStack = coreDataStack
        
        setupBindings()
    }
    
    // MARK: - Setup
    private func setupBindings() {
        // Listen for GameKit authentication changes
        gameKitService.$isAuthenticated
            .sink { [weak self] isAuthenticated in
                if isAuthenticated {
                    self?.syncWithGameCenter()
                }
            }
            .store(in: &cancellables)
    }
    
    // MARK: - Game Initialization
    
    /// Initialize the game system
    func initializeGame() async {
        gameState = .loading
        
        do {
            // Load or create player
            await loadOrCreatePlayer()
            
            // Initialize default data if needed
            await initializeDefaultData()
            
            // Setup game systems
            await setupGameSystems()
            
            await MainActor.run {
                isInitialized = true
                gameState = .menu
                print("🎮 Game Manager initialized successfully")
            }
            
        } catch {
            print("❌ Game initialization failed: \(error)")
            await MainActor.run {
                gameState = .menu // Fallback to menu even if initialization fails
            }
        }
    }
    
    @MainActor
    private func loadOrCreatePlayer() async {
        do {
            let request = Player.fetchRequest()
            request.fetchLimit = 1
            
            let players = try dataService.viewContext.fetch(request)
            
            if let existingPlayer = players.first {
                currentPlayer = existingPlayer
                existingPlayer.startSession()
                print("✅ Loaded existing player: \(existingPlayer.playerName ?? "Unknown")")
            } else {
                // Create new player
                currentPlayer = Player.createNewPlayer(
                    context: dataService.viewContext,
                    name: "New Farmer"
                )
                
                try dataService.viewContext.save()
                print("✅ Created new player")
            }
        } catch {
            print("❌ Failed to load/create player: \(error)")
        }
    }
    
    private func initializeDefaultData() async {
        guard let player = currentPlayer else { return }
        
        do {
            // Create default animals if none exist
            let animalCount = try dataService.viewContext.count(for: Animal.fetchRequest())
            if animalCount == 0 {
                await createDefaultAnimals()
            }
            
            // Create default songs if none exist
            let songCount = try dataService.viewContext.count(for: Song.fetchRequest())
            if songCount == 0 {
                await createDefaultSongs()
            }
            
            // Create farm if doesn't exist
            if player.farm == nil {
                await createDefaultFarm(for: player)
            }
            
            try await MainActor.run {
                try dataService.viewContext.save()
            }
            
        } catch {
            print("❌ Failed to initialize default data: \(error)")
        }
    }
    
    @MainActor
    private func createDefaultAnimals() async {
        let defaultAnimals: [(AnimalType, AnimalRarity, Bool)] = [
            (.cow, .common, true),      // Starting animal
            (.pig, .common, false),
            (.chicken, .common, false),
            (.sheep, .rare, false),
            (.duck, .rare, false),
            (.goat, .epic, false)
        ]
        
        for (type, rarity, unlocked) in defaultAnimals {
            let animal = Animal.createAnimal(
                context: dataService.viewContext,
                type: type,
                rarity: rarity,
                unlock: unlocked
            )
            animal.player = currentPlayer
        }
        
        print("✅ Created default animals")
    }
    
    @MainActor
    private func createDefaultSongs() async {
        for songConfig in SongConfiguration.defaultSongs {
            let song = Song(context: dataService.viewContext)
            song.title = songConfig.title
            song.difficulty = songConfig.difficulty.rawValue
            song.bpm = songConfig.bpm
            song.duration = songConfig.duration
            song.fileName = "\(songConfig.title.lowercased().replacingOccurrences(of: " ", with: "_")).mp3"
            song.isUnlocked = songConfig.difficulty == .easy // Unlock easy songs by default
            
            if song.isUnlocked {
                song.unlockedAt = Date()
            }
        }
        
        print("✅ Created default songs")
    }
    
    @MainActor
    private func createDefaultFarm(for player: Player) async {
        let farm = Farm(context: dataService.viewContext)
        farm.farmName = "\(player.playerName ?? "Player")'s Farm"
        farm.level = 1
        farm.happiness = 0.7
        farm.lastUpdated = Date()
        farm.totalBuildings = 0
        farm.farmRating = 0
        farm.player = player
        
        // Create empty layout
        let layout = FarmLayout.empty
        if let layoutData = try? JSONEncoder().encode(layout) {
            farm.layoutData = layoutData
        }
        
        print("✅ Created default farm")
    }
    
    private func setupGameSystems() async {
        // Setup audio system
        _ = audioService.playMusic("menu_background", loop: true)
        
        // Authenticate with GameKit
        _ = gameKitService.authenticatePlayer()
        
        print("✅ Game systems setup complete")
    }
    
    // MARK: - Game Flow Management
    
    /// Handle rhythm game completion
    func handleRhythmGameComplete(scoreData: ScoreData, song: Song) async {
        guard let player = currentPlayer else { return }
        
        await MainActor.run {
            // Update player stats
            player.add(
                coins: Int32(scoreData.coinsEarned),
                experience: Int32(scoreData.experienceEarned)
            )
            player.addBattlePassXP(Int32(scoreData.experienceEarned))
            
            // Update or create song progress
            updateSongProgress(for: song, scoreData: scoreData, player: player)
            
            // Check for animal unlocks
            checkAnimalUnlocks(basedOn: scoreData)
            
            // Check achievements
            checkAchievements(for: player)
            
            // Save changes
            do {
                try dataService.viewContext.save()
                print("✅ Rhythm game completion processed")
            } catch {
                print("❌ Failed to save rhythm game completion: \(error)")
            }
        }
        
        // Submit score to GameKit
        if gameKitService.isAuthenticated {
            _ = gameKitService.submitRhythmScore(scoreData.totalScore)
        }
    }
    
    @MainActor
    private func updateSongProgress(for song: Song, scoreData: ScoreData, player: Player) {
        // Find existing progress or create new
        let existingProgress = player.songProgress?.first { progress in
            (progress as? SongProgress)?.song == song
        } as? SongProgress
        
        let progress = existingProgress ?? {
            let newProgress = SongProgress(context: dataService.viewContext)
            newProgress.player = player
            newProgress.song = song
            return newProgress
        }()
        
        // Update progress
        progress.timesPlayed += 1
        progress.lastPlayedAt = Date()
        
        if scoreData.totalScore > progress.highScore {
            progress.highScore = Int32(scoreData.totalScore)
        }
        
        if scoreData.combo > progress.bestCombo {
            progress.bestCombo = Int32(scoreData.combo)
        }
        
        progress.perfectHits += Int32(scoreData.perfectHits)
        progress.goodHits += Int32(scoreData.goodHits)
        progress.okayHits += Int32(scoreData.okayHits)
        progress.missedHits += Int32(scoreData.missedHits)
        
        // Mark as completed if score is high enough
        if scoreData.accuracyPercentage >= 70.0 && !progress.isCompleted {
            progress.isCompleted = true
            progress.completedAt = Date()
        }
    }
    
    @MainActor
    private func checkAnimalUnlocks(basedOn scoreData: ScoreData) {
        // Simple unlock logic based on score
        let unlockThreshold = 50000
        
        if scoreData.totalScore >= unlockThreshold {
            // Try to unlock a random locked animal
            let request = Animal.fetchRequest()
            request.predicate = NSPredicate(format: "isUnlocked == NO")
            request.fetchLimit = 1
            
            do {
                let lockedAnimals = try dataService.viewContext.fetch(request)
                if let animalToUnlock = lockedAnimals.first {
                    animalToUnlock.unlock()
                    print("🎉 Unlocked new animal: \(animalToUnlock.displayName)")
                    
                    // Play unlock sound
                    _ = audioService.playUISound(.success)
                }
            } catch {
                print("❌ Failed to check animal unlocks: \(error)")
            }
        }
    }
    
    @MainActor
    private func checkAchievements(for player: Player) {
        let achievements = player.checkAchievements()
        
        for achievementId in achievements {
            // Report to GameKit
            if gameKitService.isAuthenticated {
                switch achievementId {
                case "first_song":
                    _ = gameKitService.reportFirstSongAchievement()
                case "animal_collector":
                    _ = gameKitService.reportAnimalCollectorAchievement(animalCount: player.totalAnimalsUnlocked)
                case "perfect_combo":
                    let maxCombo = player.songProgress?.allObjects.compactMap { ($0 as? SongProgress)?.bestCombo }.max() ?? 0
                    _ = gameKitService.reportPerfectComboAchievement(comboCount: Int(maxCombo))
                default:
                    break
                }
            }
        }
    }
    
    // MARK: - Farm Management
    
    /// Handle farm building placement
    func placeBuildingOnFarm(buildingType: BuildingConfiguration.BuildingType, at position: GridPosition) async -> Bool {
        guard let player = currentPlayer,
              let farm = player.farm else { return false }
        
        // Check if player can afford the building
        let config = BuildingConfiguration.defaultBuildings.first { $0.type == buildingType }
        guard let buildingConfig = config,
              player.canAfford(coins: Int32(buildingConfig.baseCost)) else {
            return false
        }
        
        return await MainActor.run {
            // Spend coins
            player.spend(coins: Int32(buildingConfig.baseCost))
            
            // Create building
            let building = Building(context: dataService.viewContext)
            building.type = buildingType.rawValue
            building.level = 1
            building.positionX = Int32(position.x)
            building.positionY = Int32(position.y)
            building.isConstructed = true
            building.constructedAt = Date()
            building.productionRate = buildingConfig.productionRate
            building.farm = farm
            
            // Update farm stats
            farm.totalBuildings += 1
            farm.lastUpdated = Date()
            
            // Save changes
            do {
                try dataService.viewContext.save()
                print("✅ Building placed: \(buildingConfig.name)")
                return true
            } catch {
                print("❌ Failed to place building: \(error)")
                return false
            }
        }
    }
    
    // MARK: - GameKit Integration
    
    private func syncWithGameCenter() {
        guard let player = currentPlayer else { return }
        
        // Submit current stats to GameKit
        _ = gameKitService.submitRhythmScore(Int(player.songProgress?.allObjects.compactMap { ($0 as? SongProgress)?.highScore }.max() ?? 0))
        _ = gameKitService.submitFarmRating(Int(player.farm?.farmRating ?? 0))
        
        print("✅ Synced with Game Center")
    }
    
    // MARK: - Session Management
    
    /// End current game session
    func endSession() {
        guard let player = currentPlayer else { return }
        
        let sessionDuration = Date().timeIntervalSince(player.lastPlayedAt ?? Date())
        player.endSession(duration: sessionDuration)
        
        do {
            try dataService.viewContext.save()
            print("✅ Game session ended")
        } catch {
            print("❌ Failed to save session end: \(error)")
        }
    }
}
