//
//  DataService.swift
//  Moofarm
//
//  Created by Project Moothew on 28/7/25.
//

import Foundation
import CoreData
import Combine

/// Protocol defining data service operations
protocol DataServiceProtocol {
    func save() -> AnyPublisher<Void, Error>
    func fetch<T: NSManagedObject>(_ request: NSFetchRequest<T>) -> AnyPublisher<[T], Error>
    func delete<T: NSManagedObject>(_ object: T) -> AnyPublisher<Void, Error>
    func performBackgroundTask<T>(_ block: @escaping (NSManagedObjectContext) throws -> T) -> AnyPublisher<T, Error>
}

/// Core Data service implementation
class DataService: DataServiceProtocol, ObservableObject {
    
    // MARK: - Properties
    static let shared = DataService()
    
    private let persistenceController: PersistenceController
    
    var viewContext: NSManagedObjectContext {
        persistenceController.container.viewContext
    }
    
    // MARK: - Initialization
    init(persistenceController: PersistenceController = PersistenceController.shared) {
        self.persistenceController = persistenceController
        setupContext()
    }
    
    // MARK: - Setup
    private func setupContext() {
        viewContext.automaticallyMergesChangesFromParent = true
        viewContext.mergePolicy = NSMergeByPropertyObjectTrumpMergePolicy
    }
    
    // MARK: - Core Operations
    
    /// Save the main context
    func save() -> AnyPublisher<Void, Error> {
        Future { [weak self] promise in
            guard let self = self else {
                promise(.failure(DataServiceError.contextNotAvailable))
                return
            }
            
            guard self.viewContext.hasChanges else {
                promise(.success(()))
                return
            }
            
            do {
                try self.viewContext.save()
                promise(.success(()))
            } catch {
                promise(.failure(error))
            }
        }
        .eraseToAnyPublisher()
    }
    
    /// Fetch objects using a fetch request
    func fetch<T: NSManagedObject>(_ request: NSFetchRequest<T>) -> AnyPublisher<[T], Error> {
        Future { [weak self] promise in
            guard let self = self else {
                promise(.failure(DataServiceError.contextNotAvailable))
                return
            }
            
            do {
                let results = try self.viewContext.fetch(request)
                promise(.success(results))
            } catch {
                promise(.failure(error))
            }
        }
        .eraseToAnyPublisher()
    }
    
    /// Delete an object
    func delete<T: NSManagedObject>(_ object: T) -> AnyPublisher<Void, Error> {
        Future { [weak self] promise in
            guard let self = self else {
                promise(.failure(DataServiceError.contextNotAvailable))
                return
            }
            
            self.viewContext.delete(object)
            
            do {
                try self.viewContext.save()
                promise(.success(()))
            } catch {
                promise(.failure(error))
            }
        }
        .eraseToAnyPublisher()
    }
    
    /// Perform background task
    func performBackgroundTask<T>(_ block: @escaping (NSManagedObjectContext) throws -> T) -> AnyPublisher<T, Error> {
        Future { [weak self] promise in
            guard let self = self else {
                promise(.failure(DataServiceError.contextNotAvailable))
                return
            }
            
            self.persistenceController.container.performBackgroundTask { context in
                do {
                    let result = try block(context)
                    if context.hasChanges {
                        try context.save()
                    }
                    promise(.success(result))
                } catch {
                    promise(.failure(error))
                }
            }
        }
        .eraseToAnyPublisher()
    }
}

// MARK: - Convenience Methods
extension DataService {
    
    /// Create a new object of specified type
    func create<T: NSManagedObject>(_ type: T.Type) -> T {
        return T(context: viewContext)
    }
    
    /// Fetch all objects of specified type
    func fetchAll<T: NSManagedObject>(_ type: T.Type) -> AnyPublisher<[T], Error> {
        let request = NSFetchRequest<T>(entityName: String(describing: type))
        return fetch(request)
    }
    
    /// Fetch objects with predicate
    func fetch<T: NSManagedObject>(_ type: T.Type, predicate: NSPredicate) -> AnyPublisher<[T], Error> {
        let request = NSFetchRequest<T>(entityName: String(describing: type))
        request.predicate = predicate
        return fetch(request)
    }
    
    /// Fetch objects with predicate and sort descriptors
    func fetch<T: NSManagedObject>(
        _ type: T.Type,
        predicate: NSPredicate? = nil,
        sortDescriptors: [NSSortDescriptor] = [],
        limit: Int? = nil
    ) -> AnyPublisher<[T], Error> {
        let request = NSFetchRequest<T>(entityName: String(describing: type))
        request.predicate = predicate
        request.sortDescriptors = sortDescriptors
        if let limit = limit {
            request.fetchLimit = limit
        }
        return fetch(request)
    }
}

// MARK: - Error Types
enum DataServiceError: LocalizedError {
    case contextNotAvailable
    case invalidObjectType
    case saveFailed(Error)
    case fetchFailed(Error)
    case deleteFailed(Error)
    
    var errorDescription: String? {
        switch self {
        case .contextNotAvailable:
            return "Core Data context is not available"
        case .invalidObjectType:
            return "Invalid object type for Core Data operation"
        case .saveFailed(let error):
            return "Failed to save data: \(error.localizedDescription)"
        case .fetchFailed(let error):
            return "Failed to fetch data: \(error.localizedDescription)"
        case .deleteFailed(let error):
            return "Failed to delete data: \(error.localizedDescription)"
        }
    }
}
