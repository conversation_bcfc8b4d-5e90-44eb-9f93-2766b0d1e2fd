//
//  GameKitService.swift
//  Moofarm
//
//  Created by Project Moothew on 28/7/25.
//

import Foundation
import GameKit
import Combine

/// Protocol defining GameKit service operations
protocol GameKitServiceProtocol {
    func authenticatePlayer() -> AnyPublisher<Bool, Error>
    func submitScore(_ score: Int, category: String) -> AnyPublisher<Void, Error>
    func loadLeaderboards() -> AnyPublisher<[GKLeaderboard], Error>
    func reportAchievement(_ identifier: String, percentComplete: Double) -> AnyPublisher<Void, Error>
    func loadAchievements() -> AnyPublisher<[GKAchievement], Error>
}

/// GameKit service implementation for social features
class GameKitService: NSObject, GameKitServiceProtocol, ObservableObject {
    
    // MARK: - Properties
    static let shared = GameKitService()
    
    @Published var isAuthenticated: Bool = false
    @Published var localPlayer: GKLocalPlayer?
    @Published var playerDisplayName: String = ""
    
    private var authenticationSubject = PassthroughSubject<Bool, Error>()
    
    // MARK: - Leaderboard Identifiers
    enum LeaderboardID: String, CaseIterable {
        case rhythmHighScore = "com.moofarm.leaderboard.rhythm_high_score"
        case farmRating = "com.moofarm.leaderboard.farm_rating"
        case weeklyScore = "com.moofarm.leaderboard.weekly_score"
        case totalCoins = "com.moofarm.leaderboard.total_coins"
        
        var displayName: String {
            switch self {
            case .rhythmHighScore:
                return "Rhythm High Score"
            case .farmRating:
                return "Farm Rating"
            case .weeklyScore:
                return "Weekly Score"
            case .totalCoins:
                return "Total Coins"
            }
        }
    }
    
    // MARK: - Achievement Identifiers
    enum AchievementID: String, CaseIterable {
        case firstSong = "com.moofarm.achievement.first_song"
        case perfectCombo = "com.moofarm.achievement.perfect_combo"
        case animalCollector = "com.moofarm.achievement.animal_collector"
        case farmBuilder = "com.moofarm.achievement.farm_builder"
        case rhythmMaster = "com.moofarm.achievement.rhythm_master"
        case socialPlayer = "com.moofarm.achievement.social_player"
        
        var displayName: String {
            switch self {
            case .firstSong:
                return "First Song"
            case .perfectCombo:
                return "Perfect Combo"
            case .animalCollector:
                return "Animal Collector"
            case .farmBuilder:
                return "Farm Builder"
            case .rhythmMaster:
                return "Rhythm Master"
            case .socialPlayer:
                return "Social Player"
            }
        }
        
        var description: String {
            switch self {
            case .firstSong:
                return "Complete your first rhythm game song"
            case .perfectCombo:
                return "Achieve a 50+ perfect hit combo"
            case .animalCollector:
                return "Collect 10 different animals"
            case .farmBuilder:
                return "Build 20 structures on your farm"
            case .rhythmMaster:
                return "Score 100,000+ points in rhythm game"
            case .socialPlayer:
                return "Challenge 5 friends"
            }
        }
    }
    
    // MARK: - Initialization
    override init() {
        super.init()
        setupGameKit()
    }
    
    // MARK: - Setup
    private func setupGameKit() {
        localPlayer = GKLocalPlayer.local
    }
    
    // MARK: - Authentication
    func authenticatePlayer() -> AnyPublisher<Bool, Error> {
        Future { [weak self] promise in
            guard let self = self else {
                promise(.failure(GameKitServiceError.serviceNotAvailable))
                return
            }
            
            self.localPlayer?.authenticateHandler = { [weak self] viewController, error in
                DispatchQueue.main.async {
                    if let error = error {
                        promise(.failure(error))
                        return
                    }
                    
                    if let viewController = viewController {
                        // Present authentication view controller
                        // This should be handled by the calling view
                        promise(.failure(GameKitServiceError.authenticationRequired(viewController)))
                        return
                    }
                    
                    if let localPlayer = self?.localPlayer, localPlayer.isAuthenticated {
                        self?.isAuthenticated = true
                        self?.playerDisplayName = localPlayer.displayName
                        promise(.success(true))
                    } else {
                        promise(.success(false))
                    }
                }
            }
        }
        .eraseToAnyPublisher()
    }
    
    // MARK: - Leaderboards
    func submitScore(_ score: Int, category: String) -> AnyPublisher<Void, Error> {
        Future { [weak self] promise in
            guard let self = self, self.isAuthenticated else {
                promise(.failure(GameKitServiceError.notAuthenticated))
                return
            }
            
            let scoreReporter = GKScore(leaderboardIdentifier: category)
            scoreReporter.value = Int64(score)
            
            GKScore.report([scoreReporter]) { error in
                if let error = error {
                    promise(.failure(error))
                } else {
                    promise(.success(()))
                }
            }
        }
        .eraseToAnyPublisher()
    }
    
    func loadLeaderboards() -> AnyPublisher<[GKLeaderboard], Error> {
        Future { [weak self] promise in
            guard let self = self, self.isAuthenticated else {
                promise(.failure(GameKitServiceError.notAuthenticated))
                return
            }
            
            GKLeaderboard.loadLeaderboards { leaderboards, error in
                if let error = error {
                    promise(.failure(error))
                } else {
                    promise(.success(leaderboards ?? []))
                }
            }
        }
        .eraseToAnyPublisher()
    }
    
    // MARK: - Achievements
    func reportAchievement(_ identifier: String, percentComplete: Double) -> AnyPublisher<Void, Error> {
        Future { [weak self] promise in
            guard let self = self, self.isAuthenticated else {
                promise(.failure(GameKitServiceError.notAuthenticated))
                return
            }
            
            let achievement = GKAchievement(identifier: identifier)
            achievement.percentComplete = percentComplete
            achievement.showsCompletionBanner = true
            
            GKAchievement.report([achievement]) { error in
                if let error = error {
                    promise(.failure(error))
                } else {
                    promise(.success(()))
                }
            }
        }
        .eraseToAnyPublisher()
    }
    
    func loadAchievements() -> AnyPublisher<[GKAchievement], Error> {
        Future { [weak self] promise in
            guard let self = self, self.isAuthenticated else {
                promise(.failure(GameKitServiceError.notAuthenticated))
                return
            }
            
            GKAchievement.loadAchievements { achievements, error in
                if let error = error {
                    promise(.failure(error))
                } else {
                    promise(.success(achievements ?? []))
                }
            }
        }
        .eraseToAnyPublisher()
    }
    
    // MARK: - Convenience Methods
    func submitRhythmScore(_ score: Int) -> AnyPublisher<Void, Error> {
        return submitScore(score, category: LeaderboardID.rhythmHighScore.rawValue)
    }
    
    func submitFarmRating(_ rating: Int) -> AnyPublisher<Void, Error> {
        return submitScore(rating, category: LeaderboardID.farmRating.rawValue)
    }
    
    func reportFirstSongAchievement() -> AnyPublisher<Void, Error> {
        return reportAchievement(AchievementID.firstSong.rawValue, percentComplete: 100.0)
    }
    
    func reportPerfectComboAchievement(comboCount: Int) -> AnyPublisher<Void, Error> {
        let percentComplete = min(100.0, Double(comboCount) / 50.0 * 100.0)
        return reportAchievement(AchievementID.perfectCombo.rawValue, percentComplete: percentComplete)
    }
    
    func reportAnimalCollectorAchievement(animalCount: Int) -> AnyPublisher<Void, Error> {
        let percentComplete = min(100.0, Double(animalCount) / 10.0 * 100.0)
        return reportAchievement(AchievementID.animalCollector.rawValue, percentComplete: percentComplete)
    }
}

// MARK: - Error Types
enum GameKitServiceError: LocalizedError {
    case serviceNotAvailable
    case notAuthenticated
    case authenticationRequired(UIViewController)
    case leaderboardNotFound
    case achievementNotFound
    
    var errorDescription: String? {
        switch self {
        case .serviceNotAvailable:
            return "GameKit service is not available"
        case .notAuthenticated:
            return "Player is not authenticated with Game Center"
        case .authenticationRequired:
            return "Game Center authentication required"
        case .leaderboardNotFound:
            return "Leaderboard not found"
        case .achievementNotFound:
            return "Achievement not found"
        }
    }
}
