//
//  AudioService.swift
//  Moofarm
//
//  Created by Project Moothew on 28/7/25.
//

import Foundation
import AVFoundation
import Combine

/// Protocol defining audio service operations
protocol AudioServiceProtocol {
    func playMusic(_ fileName: String, loop: Bool) -> AnyPublisher<Void, Error>
    func stopMusic() -> AnyPublisher<Void, Error>
    func playSoundEffect(_ fileName: String, volume: Float) -> AnyPublisher<Void, Error>
    func setMusicVolume(_ volume: Float)
    func setSFXVolume(_ volume: Float)
    func startBeatDetection(bpm: Double) -> AnyPublisher<Void, Never>
    func stopBeatDetection()
}

/// AVFoundation-based audio service implementation
class AudioService: NSObject, AudioServiceProtocol, ObservableObject {
    
    // MARK: - Properties
    static let shared = AudioService()
    
    @Published var isMusicPlaying: Bool = false
    @Published var currentBPM: Double = 120.0
    @Published var musicVolume: Float = 0.7
    @Published var sfxVolume: Float = 0.8
    
    private var musicPlayer: AVAudioPlayer?
    private var sfxPlayers: [String: AVAudioPlayer] = [:]
    private var audioEngine: AVAudioEngine
    private var beatTimer: Timer?
    
    // Beat detection publisher
    private let beatSubject = PassthroughSubject<Void, Never>()
    var beatPublisher: AnyPublisher<Void, Never> {
        beatSubject.eraseToAnyPublisher()
    }
    
    // MARK: - Initialization
    override init() {
        self.audioEngine = AVAudioEngine()
        super.init()
        setupAudioSession()
        preloadSoundEffects()
    }
    
    // MARK: - Setup
    private func setupAudioSession() {
        do {
            let audioSession = AVAudioSession.sharedInstance()
            try audioSession.setCategory(.playback, mode: .default, options: [.mixWithOthers])
            try audioSession.setActive(true)
        } catch {
            print("Failed to setup audio session: \(error)")
        }
    }
    
    private func preloadSoundEffects() {
        let soundEffects = [
            "cow_moo", "pig_oink", "chicken_cluck", "sheep_baa",
            "duck_quack", "goat_bleat", "ui_tap", "ui_success",
            "ui_error", "coin_collect", "level_up", "perfect_hit"
        ]
        
        for soundName in soundEffects {
            preloadSoundEffect(soundName)
        }
    }
    
    private func preloadSoundEffect(_ fileName: String) {
        guard let url = Bundle.main.url(forResource: fileName, withExtension: "wav") else {
            print("Sound file not found: \(fileName).wav")
            return
        }
        
        do {
            let player = try AVAudioPlayer(contentsOf: url)
            player.prepareToPlay()
            player.volume = sfxVolume
            sfxPlayers[fileName] = player
        } catch {
            print("Failed to preload sound effect \(fileName): \(error)")
        }
    }
    
    // MARK: - Music Playback
    func playMusic(_ fileName: String, loop: Bool = true) -> AnyPublisher<Void, Error> {
        Future { [weak self] promise in
            guard let self = self else {
                promise(.failure(AudioServiceError.serviceNotAvailable))
                return
            }
            
            guard let url = Bundle.main.url(forResource: fileName, withExtension: "mp3") else {
                promise(.failure(AudioServiceError.fileNotFound(fileName)))
                return
            }
            
            do {
                self.musicPlayer = try AVAudioPlayer(contentsOf: url)
                self.musicPlayer?.delegate = self
                self.musicPlayer?.numberOfLoops = loop ? -1 : 0
                self.musicPlayer?.volume = self.musicVolume
                self.musicPlayer?.prepareToPlay()
                
                if self.musicPlayer?.play() == true {
                    DispatchQueue.main.async {
                        self.isMusicPlaying = true
                    }
                    promise(.success(()))
                } else {
                    promise(.failure(AudioServiceError.playbackFailed))
                }
            } catch {
                promise(.failure(error))
            }
        }
        .eraseToAnyPublisher()
    }
    
    func stopMusic() -> AnyPublisher<Void, Error> {
        Future { [weak self] promise in
            guard let self = self else {
                promise(.failure(AudioServiceError.serviceNotAvailable))
                return
            }
            
            self.musicPlayer?.stop()
            DispatchQueue.main.async {
                self.isMusicPlaying = false
            }
            promise(.success(()))
        }
        .eraseToAnyPublisher()
    }
    
    // MARK: - Sound Effects
    func playSoundEffect(_ fileName: String, volume: Float = 1.0) -> AnyPublisher<Void, Error> {
        Future { [weak self] promise in
            guard let self = self else {
                promise(.failure(AudioServiceError.serviceNotAvailable))
                return
            }
            
            if let player = self.sfxPlayers[fileName] {
                player.volume = self.sfxVolume * volume
                player.stop()
                player.currentTime = 0
                player.play()
                promise(.success(()))
            } else {
                // Try to load and play if not preloaded
                guard let url = Bundle.main.url(forResource: fileName, withExtension: "wav") else {
                    promise(.failure(AudioServiceError.fileNotFound(fileName)))
                    return
                }
                
                do {
                    let player = try AVAudioPlayer(contentsOf: url)
                    player.volume = self.sfxVolume * volume
                    player.play()
                    self.sfxPlayers[fileName] = player
                    promise(.success(()))
                } catch {
                    promise(.failure(error))
                }
            }
        }
        .eraseToAnyPublisher()
    }
    
    // MARK: - Volume Control
    func setMusicVolume(_ volume: Float) {
        musicVolume = max(0.0, min(1.0, volume))
        musicPlayer?.volume = musicVolume
    }
    
    func setSFXVolume(_ volume: Float) {
        sfxVolume = max(0.0, min(1.0, volume))
        sfxPlayers.values.forEach { $0.volume = sfxVolume }
    }
    
    // MARK: - Beat Detection
    func startBeatDetection(bpm: Double) -> AnyPublisher<Void, Never> {
        stopBeatDetection()
        
        currentBPM = bpm
        let interval = 60.0 / bpm
        
        beatTimer = Timer.scheduledTimer(withTimeInterval: interval, repeats: true) { [weak self] _ in
            self?.beatSubject.send(())
        }
        
        return Just(()).eraseToAnyPublisher()
    }
    
    func stopBeatDetection() {
        beatTimer?.invalidate()
        beatTimer = nil
    }
    
    // MARK: - Convenience Methods
    func playAnimalSound(_ animalType: String) -> AnyPublisher<Void, Error> {
        let soundMap = [
            "cow": "cow_moo",
            "pig": "pig_oink",
            "chicken": "chicken_cluck",
            "sheep": "sheep_baa",
            "duck": "duck_quack",
            "goat": "goat_bleat"
        ]
        
        let soundFile = soundMap[animalType] ?? "cow_moo"
        return playSoundEffect(soundFile)
    }
    
    func playUISound(_ type: UISound) -> AnyPublisher<Void, Error> {
        return playSoundEffect(type.fileName)
    }
}

// MARK: - AVAudioPlayerDelegate
extension AudioService: AVAudioPlayerDelegate {
    func audioPlayerDidFinishPlaying(_ player: AVAudioPlayer, successfully flag: Bool) {
        if player == musicPlayer {
            DispatchQueue.main.async {
                self.isMusicPlaying = false
            }
        }
    }
}

// MARK: - UI Sound Types
enum UISound: String, CaseIterable {
    case tap = "ui_tap"
    case success = "ui_success"
    case error = "ui_error"
    case coinCollect = "coin_collect"
    case levelUp = "level_up"
    case perfectHit = "perfect_hit"
    
    var fileName: String {
        return self.rawValue
    }
}

// MARK: - Error Types
enum AudioServiceError: LocalizedError {
    case serviceNotAvailable
    case fileNotFound(String)
    case playbackFailed
    case audioSessionSetupFailed
    
    var errorDescription: String? {
        switch self {
        case .serviceNotAvailable:
            return "Audio service is not available"
        case .fileNotFound(let fileName):
            return "Audio file not found: \(fileName)"
        case .playbackFailed:
            return "Audio playback failed"
        case .audioSessionSetupFailed:
            return "Failed to setup audio session"
        }
    }
}
