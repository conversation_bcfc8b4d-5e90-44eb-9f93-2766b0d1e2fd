//
//  PlayerHelpers.swift
//  Moofarm
//
//  Created by Project Moothew on 28/7/25.
//

import Foundation
import CoreData

// MARK: - Player Helper Methods
extension Player {
    
    // MARK: - Computed Properties
    var totalAnimalsUnlocked: Int32 {
        return Int32(animals?.allObjects.compactMap { $0 as? Animal }.filter { $0.isUnlocked }.count ?? 0)
    }
    
    var isPremiumActive: Bool {
        // In production, this would check IAP status
        return false // Placeholder
    }
    
    // MARK: - Currency Management
    func canAfford(coins: Int32, gems: Int32 = 0) -> Bool {
        return self.coins >= coins && self.gems >= gems
    }
    
    func spend(coins: Int32, gems: Int32 = 0) {
        if canAfford(coins: coins, gems: gems) {
            self.coins -= coins
            self.gems -= gems
        }
    }
    
    func add(coins: Int32 = 0, gems: Int32 = 0, experience: Int32 = 0) {
        self.coins += coins
        self.gems += gems
        self.experience += experience
    }
    
    // MARK: - Battle Pass
    func addBattlePassXP(_ xp: Int32) {
        // Placeholder - would need battlePassXP property in Core Data
        print("Added \(xp) Battle Pass XP")
    }
    
    // MARK: - Session Management
    func startSession() {
        lastPlayedAt = Date()
    }

    func endSession(duration: TimeInterval) {
        // Placeholder - would need totalPlayTime property in Core Data
        lastPlayedAt = Date()
        print("Session ended after \(duration) seconds")
    }
    
    // MARK: - Level Calculation
    var currentLevel: Int32 {
        // Simple level calculation: 1000 XP per level
        return max(1, experience / 1000)
    }
    
    var experienceToNextLevel: Int32 {
        let currentLevelXP = currentLevel * 1000
        let nextLevelXP = (currentLevel + 1) * 1000
        return nextLevelXP - experience
    }
    
    var experienceProgress: Float {
        let currentLevelXP = currentLevel * 1000
        let nextLevelXP = (currentLevel + 1) * 1000
        let progressXP = experience - currentLevelXP
        return Float(progressXP) / Float(nextLevelXP - currentLevelXP)
    }
}
