//
//  Animal+Extensions.swift
//  Moofarm
//
//  Created by Project Moothew on 28/7/25.
//

import Foundation
import CoreData

// MARK: - Animal Type Enum
enum AnimalType: String, CaseIterable {
    case cow = "cow"
    case pig = "pig"
    case chicken = "chicken"
    case sheep = "sheep"
    case duck = "duck"
    case goat = "goat"
    case horse = "horse"
    case rabbit = "rabbit"
    case cat = "cat"
    case dog = "dog"
    case turkey = "turkey"
    case llama = "llama"
    
    var displayName: String {
        switch self {
        case .cow: return "Cow"
        case .pig: return "Pig"
        case .chicken: return "Chicken"
        case .sheep: return "Sheep"
        case .duck: return "Duck"
        case .goat: return "Goat"
        case .horse: return "Horse"
        case .rabbit: return "Rabbit"
        case .cat: return "Cat"
        case .dog: return "Dog"
        case .turkey: return "Turkey"
        case .llama: return "Llama"
        }
    }
    
    var emoji: String {
        switch self {
        case .cow: return "🐄"
        case .pig: return "🐷"
        case .chicken: return "🐔"
        case .sheep: return "🐑"
        case .duck: return "🦆"
        case .goat: return "🐐"
        case .horse: return "🐴"
        case .rabbit: return "🐰"
        case .cat: return "🐱"
        case .dog: return "🐶"
        case .turkey: return "🦃"
        case .llama: return "🦙"
        }
    }
    
    var soundFileName: String {
        return "\(rawValue)_sound"
    }
}

// MARK: - Animal Rarity Enum
enum AnimalRarity: String, CaseIterable {
    case common = "common"
    case rare = "rare"
    case epic = "epic"
    case legendary = "legendary"
    
    var displayName: String {
        switch self {
        case .common: return "Common"
        case .rare: return "Rare"
        case .epic: return "Epic"
        case .legendary: return "Legendary"
        }
    }
    
    var color: String {
        switch self {
        case .common: return "#808080"    // Gray
        case .rare: return "#0080FF"     // Blue
        case .epic: return "#8000FF"     // Purple
        case .legendary: return "#FFD700" // Gold
        }
    }
    
    var unlockProbability: Double {
        switch self {
        case .common: return 0.60      // 60%
        case .rare: return 0.30        // 30%
        case .epic: return 0.08        // 8%
        case .legendary: return 0.02   // 2%
        }
    }
    
    var baseHappiness: Float {
        switch self {
        case .common: return 0.7
        case .rare: return 0.8
        case .epic: return 0.9
        case .legendary: return 1.0
        }
    }
    
    var maxLevel: Int32 {
        switch self {
        case .common: return 10
        case .rare: return 15
        case .epic: return 20
        case .legendary: return 25
        }
    }
}

// MARK: - Animal Extensions
extension Animal {
    
    // MARK: - Computed Properties
    
    /// Animal type as enum
    var animalType: AnimalType? {
        return AnimalType(rawValue: type ?? "")
    }
    
    /// Animal rarity as enum
    var animalRarity: AnimalRarity? {
        return AnimalRarity(rawValue: rarity ?? "")
    }
    
    /// Display name for the animal
    var displayName: String {
        return animalType?.displayName ?? "Unknown"
    }
    
    /// Emoji representation
    var emoji: String {
        return animalType?.emoji ?? "🐾"
    }
    
    /// Is animal at maximum level
    var isMaxLevel: Bool {
        return level >= (animalRarity?.maxLevel ?? 10)
    }
    
    /// Experience needed for next level
    var experienceToNextLevel: Int {
        if isMaxLevel { return 0 }
        return Int(level + 1) * 100 // 100 XP per level
    }
    
    /// Days since unlocked
    var daysSinceUnlocked: Int {
        guard let unlockedDate = unlockedAt else { return 0 }
        return Calendar.current.dateComponents([.day], from: unlockedDate, to: Date()).day ?? 0
    }
    
    /// Needs feeding (happiness below 0.5 or not fed in 24 hours)
    var needsFeeding: Bool {
        if happiness < 0.5 { return true }
        
        guard let lastFed = lastFedAt else { return true }
        let hoursSinceFeeding = Date().timeIntervalSince(lastFed) / 3600
        return hoursSinceFeeding > 24
    }
    
    /// Happiness status
    var happinessStatus: String {
        switch happiness {
        case 0.8...1.0: return "Very Happy"
        case 0.6..<0.8: return "Happy"
        case 0.4..<0.6: return "Neutral"
        case 0.2..<0.4: return "Sad"
        default: return "Very Sad"
        }
    }
    
    /// Sound file name for this animal
    var soundFileName: String {
        return animalType?.soundFileName ?? "default_sound"
    }
    
    // MARK: - Care Management
    
    /// Feed the animal (increases happiness)
    func feed() {
        let happinessIncrease: Float = 0.2
        happiness = min(1.0, happiness + happinessIncrease)
        lastFedAt = Date()
        totalFeedings += 1
        
        // Add experience for feeding
        if !isMaxLevel {
            // TODO: Add experience system
        }
    }
    
    /// Update happiness over time (called periodically)
    func updateHappiness() {
        guard let lastFed = lastFedAt else {
            happiness = max(0.0, happiness - 0.1)
            return
        }
        
        let hoursSinceFeeding = Date().timeIntervalSince(lastFed) / 3600
        let happinessDecay = Float(hoursSinceFeeding) * 0.01 // 1% per hour
        happiness = max(0.0, happiness - happinessDecay)
    }
    
    /// Level up the animal
    func levelUp() {
        guard !isMaxLevel else { return }
        
        level += 1
        
        // Increase happiness when leveling up
        happiness = min(1.0, happiness + 0.1)
        
        // TODO: Trigger level up effects
        // - Unlock new sound variants
        // - Increase farm productivity
        // - Generate new songs
    }
    
    // MARK: - Song Generation
    
    /// Generate a new song for this animal
    func generateSong(context: NSManagedObjectContext) -> Song? {
        guard isUnlocked else { return nil }
        
        let song = Song(context: context)
        song.title = "\(displayName) \(generateSongTitle())"
        song.difficulty = generateDifficulty()
        song.bpm = generateBPM()
        song.duration = generateDuration()
        song.fileName = "\(type ?? "unknown")_song_\(level).mp3"
        song.isUnlocked = true
        song.unlockedAt = Date()
        song.sourceAnimal = self
        
        return song
    }
    
    private func generateSongTitle() -> String {
        let titles = ["Melody", "Beat", "Rhythm", "Song", "Tune", "Harmony", "Symphony"]
        return titles.randomElement() ?? "Song"
    }
    
    private func generateDifficulty() -> String {
        let rarityWeight = animalRarity?.rawValue ?? "common"
        switch rarityWeight {
        case "common": return ["easy", "normal"].randomElement() ?? "easy"
        case "rare": return ["normal", "hard"].randomElement() ?? "normal"
        case "epic": return ["normal", "hard"].randomElement() ?? "hard"
        case "legendary": return "hard"
        default: return "easy"
        }
    }
    
    private func generateBPM() -> Double {
        let baseBPM = 120.0
        let levelModifier = Double(level) * 2.0
        let rarityModifier = Double((animalRarity?.rawValue.count ?? 1) * 5)
        return baseBPM + levelModifier + rarityModifier
    }
    
    private func generateDuration() -> Double {
        return Double.random(in: 60...120) // 1-2 minutes
    }
    
    // MARK: - Factory Methods
    
    /// Create a new animal with specified type and rarity
    static func createAnimal(
        context: NSManagedObjectContext,
        type: AnimalType,
        rarity: AnimalRarity,
        unlock: Bool = false
    ) -> Animal {
        let animal = Animal(context: context)
        animal.type = type.rawValue
        animal.rarity = rarity.rawValue
        animal.level = 1
        animal.happiness = rarity.baseHappiness
        animal.soundVariant = "\(type.rawValue)_sound_1"
        animal.isUnlocked = unlock
        animal.totalFeedings = 0
        
        if unlock {
            animal.unlockedAt = Date()
        }
        
        return animal
    }
    
    /// Unlock this animal
    func unlock() {
        guard !isUnlocked else { return }
        
        isUnlocked = true
        unlockedAt = Date()
        happiness = animalRarity?.baseHappiness ?? 0.7
        
        // Generate initial song
        if let context = managedObjectContext {
            _ = generateSong(context: context)
        }
    }
    
    // MARK: - Random Generation
    
    /// Generate a random animal type
    static func randomAnimalType() -> AnimalType {
        return AnimalType.allCases.randomElement() ?? .cow
    }
    
    /// Generate a random rarity based on probabilities
    static func randomRarity() -> AnimalRarity {
        let random = Double.random(in: 0...1)
        var cumulative = 0.0
        
        for rarity in AnimalRarity.allCases.reversed() { // Start with legendary
            cumulative += rarity.unlockProbability
            if random <= cumulative {
                return rarity
            }
        }
        
        return .common // Fallback
    }
    
    /// Create a random animal
    static func createRandomAnimal(context: NSManagedObjectContext, unlock: Bool = false) -> Animal {
        let type = randomAnimalType()
        let rarity = randomRarity()
        return createAnimal(context: context, type: type, rarity: rarity, unlock: unlock)
    }
}

// MARK: - Fetch Requests
extension Animal {
    
    /// Fetch request for all animals
    @nonobjc public class func fetchRequest() -> NSFetchRequest<Animal> {
        return NSFetchRequest<Animal>(entityName: "Animal")
    }
    
    /// Fetch request for unlocked animals
    @nonobjc public class func unlockedAnimals() -> NSFetchRequest<Animal> {
        let request = NSFetchRequest<Animal>(entityName: "Animal")
        request.predicate = NSPredicate(format: "isUnlocked == YES")
        request.sortDescriptors = [
            NSSortDescriptor(key: "unlockedAt", ascending: false),
            NSSortDescriptor(key: "rarity", ascending: false)
        ]
        return request
    }
    
    /// Fetch request for animals by type
    @nonobjc public class func animalsByType(_ type: AnimalType) -> NSFetchRequest<Animal> {
        let request = NSFetchRequest<Animal>(entityName: "Animal")
        request.predicate = NSPredicate(format: "type == %@", type.rawValue)
        request.sortDescriptors = [NSSortDescriptor(key: "level", ascending: false)]
        return request
    }
    
    /// Fetch request for animals by rarity
    @nonobjc public class func animalsByRarity(_ rarity: AnimalRarity) -> NSFetchRequest<Animal> {
        let request = NSFetchRequest<Animal>(entityName: "Animal")
        request.predicate = NSPredicate(format: "rarity == %@", rarity.rawValue)
        request.sortDescriptors = [NSSortDescriptor(key: "level", ascending: false)]
        return request
    }
    
    /// Fetch request for animals that need feeding
    @nonobjc public class func animalsNeedingCare() -> NSFetchRequest<Animal> {
        let request = NSFetchRequest<Animal>(entityName: "Animal")
        let oneDayAgo = Calendar.current.date(byAdding: .day, value: -1, to: Date()) ?? Date()
        
        request.predicate = NSPredicate(format: "isUnlocked == YES AND (happiness < 0.5 OR lastFedAt < %@)", oneDayAgo as NSDate)
        request.sortDescriptors = [NSSortDescriptor(key: "happiness", ascending: true)]
        return request
    }
}
