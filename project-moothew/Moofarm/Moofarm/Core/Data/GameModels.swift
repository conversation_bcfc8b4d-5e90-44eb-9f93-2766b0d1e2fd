//
//  GameModels.swift
//  Moofarm
//
//  Created by Project Moothew on 28/7/25.
//

import Foundation

// MARK: - Game Configuration Models

/// Configuration for rhythm game songs
struct SongConfiguration {
    let title: String
    let bpm: Double
    let difficulty: Difficulty
    let notePattern: [NoteData]
    let duration: TimeInterval
    let animalType: AnimalType
    
    static let defaultSongs: [SongConfiguration] = [
        SongConfiguration(
            title: "Moo Melody",
            bpm: 120,
            difficulty: .easy,
            notePattern: [],
            duration: 60,
            animalType: .cow
        ),
        SongConfiguration(
            title: "Barnyard Beat",
            bpm: 140,
            difficulty: .normal,
            notePattern: [],
            duration: 90,
            animalType: .pig
        ),
        SongConfiguration(
            title: "Farm Symphony",
            bpm: 160,
            difficulty: .hard,
            notePattern: [],
            duration: 120,
            animalType: .chicken
        )
    ]
}

/// Individual note data for rhythm game
struct NoteData: Codable {
    let lane: Int           // 0-3 for 4 lanes
    let timing: Double      // Time in seconds when note should be hit
    let type: NoteType      // Type of note
    let animalType: AnimalType // Which animal sound to play
    
    enum NoteType: String, Codable, CaseIterable {
        case normal = "normal"
        case hold = "hold"
        case special = "special"
        
        var displayName: String {
            switch self {
            case .normal: return "Normal"
            case .hold: return "Hold"
            case .special: return "Special"
            }
        }
    }
}

/// Score data for rhythm game results
struct ScoreData {
    let totalScore: Int
    let combo: Int
    let accuracy: Double
    let perfectHits: Int
    let goodHits: Int
    let okayHits: Int
    let missedHits: Int
    let coinsEarned: Int
    let experienceEarned: Int
    
    var totalHits: Int {
        return perfectHits + goodHits + okayHits + missedHits
    }
    
    var successfulHits: Int {
        return perfectHits + goodHits + okayHits
    }
    
    var accuracyPercentage: Double {
        guard totalHits > 0 else { return 0.0 }
        return Double(successfulHits) / Double(totalHits) * 100.0
    }
    
    var grade: String {
        switch accuracyPercentage {
        case 95...100: return "S"
        case 90..<95: return "A"
        case 80..<90: return "B"
        case 70..<80: return "C"
        case 60..<70: return "D"
        default: return "F"
        }
    }
}

// MARK: - Farm Models

/// Building configuration for farm
struct BuildingConfiguration {
    let type: BuildingType
    let name: String
    let description: String
    let baseCost: Int
    let upgradeCosts: [Int]
    let productionRate: Float
    let size: CGSize
    let maxLevel: Int
    
    enum BuildingType: String, CaseIterable {
        case barn = "barn"
        case house = "house"
        case coop = "coop"
        case pond = "pond"
        case decoration = "decoration"
        case musicHall = "music_hall"
        case practiceRoom = "practice_room"
        
        var displayName: String {
            switch self {
            case .barn: return "Barn"
            case .house: return "Farm House"
            case .coop: return "Chicken Coop"
            case .pond: return "Duck Pond"
            case .decoration: return "Decoration"
            case .musicHall: return "Music Hall"
            case .practiceRoom: return "Practice Room"
            }
        }
        
        var emoji: String {
            switch self {
            case .barn: return "🏚️"
            case .house: return "🏡"
            case .coop: return "🏠"
            case .pond: return "🏞️"
            case .decoration: return "🌸"
            case .musicHall: return "🎵"
            case .practiceRoom: return "🎼"
            }
        }
    }
    
    static let defaultBuildings: [BuildingConfiguration] = [
        BuildingConfiguration(
            type: .barn,
            name: "Barn",
            description: "Houses your farm animals",
            baseCost: 100,
            upgradeCosts: [200, 400, 800],
            productionRate: 1.0,
            size: CGSize(width: 2, height: 2),
            maxLevel: 3
        ),
        BuildingConfiguration(
            type: .house,
            name: "Farm House",
            description: "Your cozy home on the farm",
            baseCost: 500,
            upgradeCosts: [1000, 2000, 4000],
            productionRate: 0.0,
            size: CGSize(width: 3, height: 3),
            maxLevel: 3
        ),
        BuildingConfiguration(
            type: .coop,
            name: "Chicken Coop",
            description: "Perfect home for chickens",
            baseCost: 150,
            upgradeCosts: [300, 600, 1200],
            productionRate: 1.2,
            size: CGSize(width: 2, height: 1),
            maxLevel: 3
        )
    ]
}

/// Farm grid position
struct GridPosition: Codable, Hashable {
    let x: Int
    let y: Int
    
    static let zero = GridPosition(x: 0, y: 0)
    
    func distance(to other: GridPosition) -> Double {
        let dx = Double(x - other.x)
        let dy = Double(y - other.y)
        return sqrt(dx * dx + dy * dy)
    }
}

/// Farm layout data
struct FarmLayout: Codable {
    var buildings: [PlacedBuilding]
    var animals: [PlacedAnimal]
    var decorations: [PlacedDecoration]
    var gridSize: CGSize
    
    struct PlacedBuilding: Codable, Identifiable {
        let id: UUID
        let type: BuildingConfiguration.BuildingType
        let position: GridPosition
        let level: Int
        let placedAt: Date
        
        init(type: BuildingConfiguration.BuildingType, position: GridPosition, level: Int = 1) {
            self.id = UUID()
            self.type = type
            self.position = position
            self.level = level
            self.placedAt = Date()
        }
    }
    
    struct PlacedAnimal: Codable, Identifiable {
        let id: UUID
        let animalType: AnimalType
        let position: GridPosition
        let placedAt: Date
        
        init(animalType: AnimalType, position: GridPosition) {
            self.id = UUID()
            self.animalType = animalType
            self.position = position
            self.placedAt = Date()
        }
    }
    
    struct PlacedDecoration: Codable, Identifiable {
        let id: UUID
        let decorationType: String
        let position: GridPosition
        let placedAt: Date
        
        init(decorationType: String, position: GridPosition) {
            self.id = UUID()
            self.decorationType = decorationType
            self.position = position
            self.placedAt = Date()
        }
    }
    
    static let empty = FarmLayout(
        buildings: [],
        animals: [],
        decorations: [],
        gridSize: CGSize(width: 10, height: 10)
    )
}

// MARK: - Battle Pass Models

/// Battle pass tier configuration
struct BattlePassTier {
    let tier: Int
    let xpRequired: Int
    let freeReward: BattlePassReward?
    let premiumReward: BattlePassReward?
    
    struct BattlePassReward {
        let type: RewardType
        let amount: Int
        let description: String
        
        enum RewardType: String, CaseIterable {
            case coins = "coins"
            case gems = "gems"
            case animal = "animal"
            case building = "building"
            case decoration = "decoration"
            case song = "song"
            
            var displayName: String {
                switch self {
                case .coins: return "Coins"
                case .gems: return "Gems"
                case .animal: return "Animal"
                case .building: return "Building"
                case .decoration: return "Decoration"
                case .song: return "Song"
                }
            }
            
            var emoji: String {
                switch self {
                case .coins: return "🪙"
                case .gems: return "💎"
                case .animal: return "🐾"
                case .building: return "🏗️"
                case .decoration: return "🌸"
                case .song: return "🎵"
                }
            }
        }
    }
}

/// Battle pass season configuration
struct BattlePassSeason {
    let seasonNumber: Int
    let name: String
    let description: String
    let startDate: Date
    let endDate: Date
    let tiers: [BattlePassTier]
    let theme: String
    
    var isActive: Bool {
        let now = Date()
        return now >= startDate && now <= endDate
    }
    
    var daysRemaining: Int {
        let now = Date()
        guard now <= endDate else { return 0 }
        return Calendar.current.dateComponents([.day], from: now, to: endDate).day ?? 0
    }
    
    static let defaultSeason = BattlePassSeason(
        seasonNumber: 1,
        name: "Farm Beginnings",
        description: "Start your farming journey with exclusive rewards!",
        startDate: Date(),
        endDate: Calendar.current.date(byAdding: .day, value: 60, to: Date()) ?? Date(),
        tiers: [],
        theme: "farm_spring"
    )
}

// MARK: - Achievement Models

/// Achievement configuration
struct Achievement {
    let id: String
    let name: String
    let description: String
    let category: AchievementCategory
    let requirement: AchievementRequirement
    let reward: BattlePassTier.BattlePassReward
    let isSecret: Bool
    
    enum AchievementCategory: String, CaseIterable {
        case rhythm = "rhythm"
        case farm = "farm"
        case collection = "collection"
        case social = "social"
        case progression = "progression"
        
        var displayName: String {
            switch self {
            case .rhythm: return "Rhythm Master"
            case .farm: return "Farm Builder"
            case .collection: return "Collector"
            case .social: return "Social"
            case .progression: return "Progress"
            }
        }
        
        var emoji: String {
            switch self {
            case .rhythm: return "🎵"
            case .farm: return "🏡"
            case .collection: return "🐾"
            case .social: return "👥"
            case .progression: return "📈"
            }
        }
    }
    
    enum AchievementRequirement {
        case songCount(Int)
        case perfectHits(Int)
        case animalCount(Int)
        case buildingCount(Int)
        case totalScore(Int)
        case playTime(TimeInterval)
        case consecutiveDays(Int)
        
        var description: String {
            switch self {
            case .songCount(let count):
                return "Complete \(count) songs"
            case .perfectHits(let count):
                return "Get \(count) perfect hits"
            case .animalCount(let count):
                return "Collect \(count) animals"
            case .buildingCount(let count):
                return "Build \(count) structures"
            case .totalScore(let score):
                return "Reach \(score) total score"
            case .playTime(let time):
                return "Play for \(Int(time/3600)) hours"
            case .consecutiveDays(let days):
                return "Play for \(days) consecutive days"
            }
        }
    }
}
