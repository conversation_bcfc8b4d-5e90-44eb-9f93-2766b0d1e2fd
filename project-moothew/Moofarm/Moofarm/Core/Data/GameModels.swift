//
//  GameModels.swift
//  Moofarm
//
//  Created by Project Moothew on 28/7/25.
//

import Foundation

// MARK: - Game Configuration Models

/// Configuration for rhythm game songs
struct SongConfiguration {
    let title: String
    let bpm: Double
    let difficulty: Difficulty
    let notePattern: [NoteData]
    let duration: TimeInterval
    let animalType: AnimalType
    
    static let defaultSongs: [SongConfiguration] = [
        SongConfiguration(
            title: "Moo Melody",
            bpm: 120,
            difficulty: .easy,
            notePattern: [],
            duration: 60,
            animalType: .cow
        ),
        SongConfiguration(
            title: "Barnyard Beat",
            bpm: 140,
            difficulty: .normal,
            notePattern: [],
            duration: 90,
            animalType: .pig
        ),
        SongConfiguration(
            title: "Farm Symphony",
            bpm: 160,
            difficulty: .hard,
            notePattern: [],
            duration: 120,
            animalType: .chicken
        )
    ]
}

/// Individual note data for rhythm game
struct NoteData: Codable {
    let lane: Int           // 0-3 for 4 lanes
    let timing: Double      // Time in seconds when note should be hit
    let type: NoteType      // Type of note
    let animalType: AnimalType // Which animal sound to play
    
    enum NoteType: String, Codable, CaseIterable {
        case normal = "normal"
        case hold = "hold"
        case special = "special"
        
        var displayName: String {
            switch self {
            case .normal: return "Normal"
            case .hold: return "Hold"
            case .special: return "Special"
            }
        }
    }
}

/// Score data for rhythm game results
struct ScoreData {
    let totalScore: Int
    let combo: Int
    let accuracy: Double
    let perfectHits: Int
    let goodHits: Int
    let okayHits: Int
    let missedHits: Int
    let coinsEarned: Int
    let experienceEarned: Int
    
    var totalHits: Int {
        return perfectHits + goodHits + okayHits + missedHits
    }
    
    var successfulHits: Int {
        return perfectHits + goodHits + okayHits
    }
    
    var accuracyPercentage: Double {
        guard totalHits > 0 else { return 0.0 }
        return Double(successfulHits) / Double(totalHits) * 100.0
    }
    
    var grade: String {
        switch accuracyPercentage {
        case 95...100: return "S"
        case 90..<95: return "A"
        case 80..<90: return "B"
        case 70..<80: return "C"
        case 60..<70: return "D"
        default: return "F"
        }
    }
}

// MARK: - Farm Models
// BuildingConfiguration moved to separate file

// MARK: - Achievement Models
struct Achievement: Codable, Identifiable {
    let id: String
    let name: String
    let description: String
    let category: AchievementCategory
    let requirement: AchievementRequirement
    let reward: AchievementReward
    let points: Int
    let rarity: AchievementRarity
    let isSecret: Bool

    enum AchievementCategory: String, Codable, CaseIterable {
        case rhythm = "rhythm"
        case farm = "farm"
        case crossSystem = "cross_system"
        case social = "social"
        case collection = "collection"
        case progression = "progression"

        var displayName: String {
            switch self {
            case .rhythm: return "Rhythm"
            case .farm: return "Farm"
            case .crossSystem: return "Cross-System"
            case .social: return "Social"
            case .collection: return "Collection"
            case .progression: return "Progression"
            }
        }
    }

    enum AchievementRarity: String, Codable, CaseIterable {
        case common = "common"
        case rare = "rare"
        case epic = "epic"
        case legendary = "legendary"

        var displayName: String {
            return rawValue.capitalized
        }
    }
}

enum AchievementRequirement: Codable {
    case songCount(Int)
    case perfectHits(Int)
    case maxCombo(Int)
    case totalScore(Int)
    case buildingCount(Int)
    case farmLevel(Int)
    case farmHappiness(Int, days: Int)
    case animalCount(Int)
    case legendaryAnimals(Int)
    case consecutiveDays(Int)
    case playTime(Int)
    case farmVisits(Int)
    case friendHelp(Int)
    case crossSystemGoal(farmLevel: Int, songsCompleted: Int)
    case perfectSynergy
    case ultimateGoal(farmLevel: Int, totalScore: Int)
}

struct AchievementReward: Codable {
    let coins: Int
    let experience: Int
    let specialItem: String?
}

struct AchievementChain: Codable {
    let id: String
    let name: String
    let description: String
    let achievementIds: [String]
    let chainReward: AchievementReward
    let chainPoints: Int
}

struct MetaAchievement: Codable {
    let id: String
    let name: String
    let description: String
    let requirement: MetaAchievementRequirement
    let reward: AchievementReward
    let points: Int
}

enum MetaAchievementRequirement: Codable {
    case achievementCount(Int)
    case chainCount(Int)
    case totalPoints(Int)
}

struct AchievementUnlock: Codable {
    let achievementId: String
    let achievementName: String
    let unlockedAt: Date
    let pointsEarned: Int
}

/// Farm grid position
struct GridPosition: Codable, Hashable {
    let x: Int
    let y: Int
    
    static let zero = GridPosition(x: 0, y: 0)
    
    func distance(to other: GridPosition) -> Double {
        let dx = Double(x - other.x)
        let dy = Double(y - other.y)
        return sqrt(dx * dx + dy * dy)
    }
}

/// Farm layout data
struct FarmLayout: Codable {
    var buildings: [PlacedBuilding]
    var animals: [PlacedAnimal]
    var decorations: [PlacedDecoration]
    var gridSize: CGSize
    
    struct PlacedBuilding: Codable, Identifiable {
        let id: UUID
        let type: BuildingConfiguration.BuildingType
        let position: GridPosition
        let level: Int
        let placedAt: Date
        
        init(type: BuildingConfiguration.BuildingType, position: GridPosition, level: Int = 1) {
            self.id = UUID()
            self.type = type
            self.position = position
            self.level = level
            self.placedAt = Date()
        }
    }
    
    struct PlacedAnimal: Codable, Identifiable {
        let id: UUID
        let animalType: AnimalType
        let position: GridPosition
        let placedAt: Date
        
        init(animalType: AnimalType, position: GridPosition) {
            self.id = UUID()
            self.animalType = animalType
            self.position = position
            self.placedAt = Date()
        }
    }
    
    struct PlacedDecoration: Codable, Identifiable {
        let id: UUID
        let decorationType: String
        let position: GridPosition
        let placedAt: Date
        
        init(decorationType: String, position: GridPosition) {
            self.id = UUID()
            self.decorationType = decorationType
            self.position = position
            self.placedAt = Date()
        }
    }
    
    static let empty = FarmLayout(
        buildings: [],
        animals: [],
        decorations: [],
        gridSize: CGSize(width: 10, height: 10)
    )
}

// MARK: - Battle Pass Models

/// Battle pass tier configuration
struct BattlePassTier {
    let tier: Int
    let xpRequired: Int
    let freeReward: BattlePassReward?
    let premiumReward: BattlePassReward?
    
    struct BattlePassReward {
        let type: RewardType
        let amount: Int
        let description: String
        
        enum RewardType: String, CaseIterable {
            case coins = "coins"
            case gems = "gems"
            case animal = "animal"
            case building = "building"
            case decoration = "decoration"
            case song = "song"
            
            var displayName: String {
                switch self {
                case .coins: return "Coins"
                case .gems: return "Gems"
                case .animal: return "Animal"
                case .building: return "Building"
                case .decoration: return "Decoration"
                case .song: return "Song"
                }
            }
            
            var emoji: String {
                switch self {
                case .coins: return "🪙"
                case .gems: return "💎"
                case .animal: return "🐾"
                case .building: return "🏗️"
                case .decoration: return "🌸"
                case .song: return "🎵"
                }
            }
        }
    }
}

/// Battle pass season configuration
struct BattlePassSeason {
    let seasonNumber: Int
    let name: String
    let description: String
    let startDate: Date
    let endDate: Date
    let tiers: [BattlePassTier]
    let theme: String
    
    var isActive: Bool {
        let now = Date()
        return now >= startDate && now <= endDate
    }
    
    var daysRemaining: Int {
        let now = Date()
        guard now <= endDate else { return 0 }
        return Calendar.current.dateComponents([.day], from: now, to: endDate).day ?? 0
    }
    
    static let defaultSeason = BattlePassSeason(
        seasonNumber: 1,
        name: "Farm Beginnings",
        description: "Start your farming journey with exclusive rewards!",
        startDate: Date(),
        endDate: Calendar.current.date(byAdding: .day, value: 60, to: Date()) ?? Date(),
        tiers: [],
        theme: "farm_spring"
    )
}
