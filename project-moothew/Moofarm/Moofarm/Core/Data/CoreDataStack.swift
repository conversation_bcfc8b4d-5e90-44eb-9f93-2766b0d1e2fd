//
//  CoreDataStack.swift
//  Moofarm
//
//  Created by Project Moothew on 28/7/25.
//

import CoreData
import Foundation

/// Core Data stack management for Moo Farm game
/// Handles persistence, migrations, and context management
class CoreDataStack {
    
    // MARK: - Singleton
    static let shared = CoreDataStack()
    
    // MARK: - Preview Context
    @MainActor
    static let preview: CoreDataStack = {
        let stack = CoreDataStack(inMemory: true)
        let context = stack.viewContext
        
        // Create sample data for previews
        createSampleData(in: context)
        
        do {
            try context.save()
        } catch {
            print("Preview context save failed: \(error)")
        }
        
        return stack
    }()
    
    // MARK: - Container
    lazy var persistentContainer: NSPersistentContainer = {
        let container = NSPersistentContainer(name: "Moofarm")
        
        if inMemory {
            container.persistentStoreDescriptions.first?.url = URL(fileURLWithPath: "/dev/null")
        }
        
        // Configure persistent store
        container.persistentStoreDescriptions.first?.setOption(true as NSNumber, 
                                                               forKey: NSPersistentHistoryTrackingKey)
        container.persistentStoreDescriptions.first?.setOption(true as NSNumber, 
                                                               forKey: NSPersistentStoreRemoteChangeNotificationPostOptionKey)
        
        container.loadPersistentStores { [weak self] storeDescription, error in
            if let error = error as NSError? {
                // Log error for debugging
                print("Core Data error: \(error), \(error.userInfo)")
                
                // In production, handle this more gracefully
                #if DEBUG
                fatalError("Unresolved Core Data error \(error), \(error.userInfo)")
                #else
                // In production, you might want to delete and recreate the store
                self?.handleCoreDataError(error)
                #endif
            }
        }
        
        // Configure view context
        container.viewContext.automaticallyMergesChangesFromParent = true
        container.viewContext.mergePolicy = NSMergeByPropertyObjectTrumpMergePolicy
        
        return container
    }()
    
    // MARK: - Properties
    private let inMemory: Bool
    
    var viewContext: NSManagedObjectContext {
        return persistentContainer.viewContext
    }
    
    // MARK: - Initialization
    init(inMemory: Bool = false) {
        self.inMemory = inMemory
    }
    
    // MARK: - Context Management
    
    /// Create a new background context for heavy operations
    func newBackgroundContext() -> NSManagedObjectContext {
        let context = persistentContainer.newBackgroundContext()
        context.mergePolicy = NSMergeByPropertyObjectTrumpMergePolicy
        return context
    }
    
    /// Save the view context
    func save() throws {
        let context = viewContext
        
        if context.hasChanges {
            try context.save()
        }
    }
    
    /// Save a background context
    func save(context: NSManagedObjectContext) throws {
        if context.hasChanges {
            try context.save()
        }
    }
    
    /// Perform a background task
    func performBackgroundTask<T>(_ block: @escaping (NSManagedObjectContext) throws -> T) async throws -> T {
        return try await withCheckedThrowingContinuation { continuation in
            persistentContainer.performBackgroundTask { context in
                do {
                    let result = try block(context)
                    if context.hasChanges {
                        try context.save()
                    }
                    continuation.resume(returning: result)
                } catch {
                    continuation.resume(throwing: error)
                }
            }
        }
    }
    
    // MARK: - Error Handling
    private func handleCoreDataError(_ error: NSError) {
        // In production, implement recovery strategies:
        // 1. Try to delete and recreate the store
        // 2. Migrate data if possible
        // 3. Reset to clean state as last resort
        
        print("Handling Core Data error: \(error)")
        
        // For now, just log the error
        // TODO: Implement proper error recovery
    }
    
    // MARK: - Batch Operations
    
    /// Delete all data for a specific entity
    func deleteAll<T: NSManagedObject>(entity: T.Type) throws {
        let context = viewContext
        let fetchRequest = NSFetchRequest<NSFetchRequestResult>(entityName: String(describing: entity))
        let deleteRequest = NSBatchDeleteRequest(fetchRequest: fetchRequest)
        
        try context.execute(deleteRequest)
        try save()
    }
    
    /// Count objects for a specific entity
    func count<T: NSManagedObject>(entity: T.Type, predicate: NSPredicate? = nil) throws -> Int {
        let context = viewContext
        let fetchRequest = NSFetchRequest<T>(entityName: String(describing: entity))
        fetchRequest.predicate = predicate
        
        return try context.count(for: fetchRequest)
    }
}

// MARK: - Sample Data Creation
extension CoreDataStack {
    
    @MainActor
    private static func createSampleData(in context: NSManagedObjectContext) {
        // Create sample player
        let player = Player(context: context)
        player.level = 5
        player.experience = 1250
        player.coins = 500
        player.gems = 25
        player.playerName = "Sample Player"
        player.createdAt = Date()
        player.lastPlayedAt = Date()
        
        // Create sample animals
        let animalTypes = ["cow", "pig", "chicken", "sheep"]
        let rarities = ["common", "rare", "epic"]
        
        for (index, animalType) in animalTypes.enumerated() {
            let animal = Animal(context: context)
            animal.type = animalType
            animal.rarity = rarities[index % rarities.count]
            animal.level = Int32(index + 1)
            animal.happiness = Float.random(in: 0.5...1.0)
            animal.isUnlocked = true
            animal.unlockedAt = Date()
            animal.soundVariant = "\(animalType)_sound_1"
        }
        
        // Create sample farm
        let farm = Farm(context: context)
        farm.farmName = "Sample Farm"
        farm.level = 3
        farm.happiness = 0.85
        farm.layoutData = Data() // Empty layout for now
        farm.lastUpdated = Date()
        farm.player = player
        
        // Create sample songs
        let songNames = ["Moo Melody", "Barnyard Beat", "Farm Symphony"]
        for (index, songName) in songNames.enumerated() {
            let song = Song(context: context)
            song.title = songName
            song.difficulty = ["easy", "normal", "hard"][index % 3]
            song.bpm = Double([120, 140, 160][index % 3])
            song.duration = Double([60, 90, 120][index % 3])
            song.isUnlocked = index < 2 // First 2 songs unlocked
            song.fileName = "\(songName.lowercased().replacingOccurrences(of: " ", with: "_")).mp3"
        }
        
        print("✅ Sample data created for Core Data preview")
    }
}

// MARK: - Migration Support
extension CoreDataStack {
    
    /// Check if migration is needed
    var needsMigration: Bool {
        guard let storeURL = persistentContainer.persistentStoreDescriptions.first?.url else {
            return false
        }
        
        do {
            let metadata = try NSPersistentStoreCoordinator.metadataForPersistentStore(
                ofType: NSSQLiteStoreType,
                at: storeURL,
                options: nil
            )
            
            let model = persistentContainer.managedObjectModel
            return !model.isConfiguration(withName: nil, compatibleWithStoreMetadata: metadata)
        } catch {
            return false
        }
    }
    
    /// Perform lightweight migration
    func performMigration() throws {
        // Core Data will handle lightweight migration automatically
        // This method is here for future custom migration logic
        print("🔄 Core Data migration completed")
    }
}
