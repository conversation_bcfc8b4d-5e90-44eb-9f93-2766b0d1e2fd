//
//  Player+Extensions.swift
//  Moofarm
//
//  Created by Project Moothew on 28/7/25.
//

import Foundation
import CoreData

// MARK: - Player Extensions
extension Player {
    
    // MARK: - Computed Properties
    
    /// Current player level based on experience
    var currentLevel: PlayerLevel {
        return PlayerLevel.level(for: Int(experience))
    }
    
    /// Experience needed for next level
    var experienceToNextLevel: Int {
        let nextLevel = PlayerLevel.allCases.first { $0.requiredExperience > Int(experience) }
        return (nextLevel?.requiredExperience ?? Int(experience)) - Int(experience)
    }
    
    /// Progress to next level (0.0 to 1.0)
    var levelProgress: Float {
        let currentLevelExp = currentLevel.requiredExperience
        let nextLevelExp = PlayerLevel.allCases.first { $0.requiredExperience > Int(experience) }?.requiredExperience ?? Int(experience)
        
        if nextLevelExp == currentLevelExp {
            return 1.0
        }
        
        return Float(Int(experience) - currentLevelExp) / Float(nextLevelExp - currentLevelExp)
    }
    
    /// Total play time in hours
    var playTimeHours: Double {
        return Double(totalPlayTime) / 3600.0
    }
    
    /// Days since account creation
    var daysSinceCreation: Int {
        return Calendar.current.dateComponents([.day], from: createdAt ?? Date(), to: Date()).day ?? 0
    }
    
    /// Is player active (played within last 7 days)
    var isActivePlayer: Bool {
        guard let lastPlayed = lastPlayedAt else { return false }
        return Date().timeIntervalSince(lastPlayed) < 7 * 24 * 60 * 60 // 7 days
    }
    
    // MARK: - Battle Pass Properties
    
    /// Current battle pass tier (1-60)
    var battlePassTier: Int {
        return min(60, max(1, Int(battlePassLevel) + 1))
    }
    
    /// Progress to next battle pass tier (0.0 to 1.0)
    var battlePassProgress: Float {
        let xpPerTier = 1000 // XP needed per tier
        let currentTierXP = Int(battlePassXP) % xpPerTier
        return Float(currentTierXP) / Float(xpPerTier)
    }
    
    /// XP needed for next battle pass tier
    var xpToNextTier: Int {
        let xpPerTier = 1000
        let currentTierXP = Int(battlePassXP) % xpPerTier
        return xpPerTier - currentTierXP
    }
    
    // MARK: - Currency Management
    
    /// Check if player can afford a purchase
    func canAfford(coins: Int32 = 0, gems: Int32 = 0) -> Bool {
        return self.coins >= coins && self.gems >= gems
    }
    
    /// Spend currency (returns true if successful)
    @discardableResult
    func spend(coins: Int32 = 0, gems: Int32 = 0) -> Bool {
        guard canAfford(coins: coins, gems: gems) else {
            return false
        }
        
        self.coins -= coins
        self.gems -= gems
        return true
    }
    
    /// Add currency
    func add(coins: Int32 = 0, gems: Int32 = 0, experience: Int32 = 0) {
        self.coins += coins
        self.gems += gems
        self.experience += experience
        
        // Update level if experience increased
        if experience > 0 {
            updateLevel()
        }
    }
    
    /// Add battle pass XP
    func addBattlePassXP(_ xp: Int32) {
        battlePassXP += xp
        
        // Calculate new level
        let newLevel = Int32(battlePassXP / 1000)
        if newLevel > battlePassLevel {
            battlePassLevel = newLevel
            // TODO: Trigger battle pass reward notification
        }
    }
    
    // MARK: - Level Management
    
    private func updateLevel() {
        let newLevel = PlayerLevel.level(for: Int(experience))
        if newLevel.rawValue > level {
            level = Int32(newLevel.rawValue)
            // TODO: Trigger level up notification
        }
    }
    
    // MARK: - Statistics
    
    /// Get total animals unlocked
    var totalAnimalsUnlocked: Int {
        return unlockedAnimals?.filter { ($0 as? Animal)?.isUnlocked == true }.count ?? 0
    }
    
    /// Get total songs completed
    var totalSongsCompleted: Int {
        return songProgress?.filter { ($0 as? SongProgress)?.isCompleted == true }.count ?? 0
    }
    
    /// Get average song score
    var averageSongScore: Double {
        let progressArray = songProgress?.allObjects as? [SongProgress] ?? []
        let scores = progressArray.map { Double($0.highScore) }
        return scores.isEmpty ? 0.0 : scores.reduce(0, +) / Double(scores.count)
    }
    
    /// Get total perfect hits across all songs
    var totalPerfectHits: Int {
        let progressArray = songProgress?.allObjects as? [SongProgress] ?? []
        return progressArray.reduce(0) { $0 + Int($1.perfectHits) }
    }
    
    /// Get accuracy percentage
    var overallAccuracy: Double {
        let progressArray = songProgress?.allObjects as? [SongProgress] ?? []
        let totalHits = progressArray.reduce(0) { $0 + Int($1.perfectHits + $1.goodHits + $1.okayHits + $1.missedHits) }
        let successfulHits = progressArray.reduce(0) { $0 + Int($1.perfectHits + $1.goodHits + $1.okayHits) }
        
        return totalHits > 0 ? Double(successfulHits) / Double(totalHits) * 100.0 : 0.0
    }
    
    // MARK: - Achievements
    
    /// Check if player qualifies for specific achievements
    func checkAchievements() -> [String] {
        var achievements: [String] = []
        
        // First song achievement
        if totalSongsCompleted >= 1 {
            achievements.append("first_song")
        }
        
        // Animal collector achievement
        if totalAnimalsUnlocked >= 10 {
            achievements.append("animal_collector")
        }
        
        // Farm builder achievement
        if farm?.totalBuildings ?? 0 >= 20 {
            achievements.append("farm_builder")
        }
        
        // Rhythm master achievement
        if averageSongScore >= 100000 {
            achievements.append("rhythm_master")
        }
        
        // Perfect combo achievement
        let maxCombo = songProgress?.allObjects.compactMap { ($0 as? SongProgress)?.bestCombo }.max() ?? 0
        if maxCombo >= 50 {
            achievements.append("perfect_combo")
        }
        
        return achievements
    }
    
    // MARK: - Session Management
    
    /// Start a new play session
    func startSession() {
        lastPlayedAt = Date()
    }
    
    /// End current play session
    func endSession(duration: TimeInterval) {
        totalPlayTime += Int32(duration)
        lastPlayedAt = Date()
    }
    
    // MARK: - Factory Methods
    
    /// Create a new player with default values
    static func createNewPlayer(context: NSManagedObjectContext, name: String? = nil) -> Player {
        let player = Player(context: context)
        player.level = 1
        player.experience = 0
        player.coins = 100 // Starting coins
        player.gems = 0
        player.playerName = name ?? "New Player"
        player.createdAt = Date()
        player.lastPlayedAt = Date()
        player.totalPlayTime = 0
        player.battlePassLevel = 0
        player.battlePassXP = 0
        player.hasPremiumBattlePass = false
        
        return player
    }
}

// MARK: - Fetch Requests
extension Player {
    
    /// Fetch request for all players
    @nonobjc public class func fetchRequest() -> NSFetchRequest<Player> {
        return NSFetchRequest<Player>(entityName: "Player")
    }
    
    /// Fetch request for active players (played within last 30 days)
    @nonobjc public class func activePlayers() -> NSFetchRequest<Player> {
        let request = NSFetchRequest<Player>(entityName: "Player")
        let thirtyDaysAgo = Calendar.current.date(byAdding: .day, value: -30, to: Date()) ?? Date()
        request.predicate = NSPredicate(format: "lastPlayedAt >= %@", thirtyDaysAgo as NSDate)
        request.sortDescriptors = [NSSortDescriptor(key: "lastPlayedAt", ascending: false)]
        return request
    }
    
    /// Fetch request for players by level range
    @nonobjc public class func playersByLevel(min: Int32, max: Int32) -> NSFetchRequest<Player> {
        let request = NSFetchRequest<Player>(entityName: "Player")
        request.predicate = NSPredicate(format: "level >= %d AND level <= %d", min, max)
        request.sortDescriptors = [NSSortDescriptor(key: "level", ascending: false)]
        return request
    }
}
