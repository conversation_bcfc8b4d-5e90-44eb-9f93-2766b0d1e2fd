//
//  AnimalHelpers.swift
//  Moofarm
//
//  Created by Project Moothew on 28/7/25.
//

import Foundation
import CoreData

// MARK: - Animal Type Enum
enum AnimalType: String, CaseIterable, Codable {
    case cow = "cow"
    case pig = "pig"
    case chicken = "chicken"
    case sheep = "sheep"
    case duck = "duck"
    case goat = "goat"
    case horse = "horse"
    case rabbit = "rabbit"
    case cat = "cat"
    case dog = "dog"
    case turkey = "turkey"
    case llama = "llama"
    
    var displayName: String {
        return rawValue.capitalized
    }
    
    var emoji: String {
        switch self {
        case .cow: return "🐄"
        case .pig: return "🐷"
        case .chicken: return "🐔"
        case .sheep: return "🐑"
        case .duck: return "🦆"
        case .goat: return "🐐"
        case .horse: return "🐴"
        case .rabbit: return "🐰"
        case .cat: return "🐱"
        case .dog: return "🐶"
        case .turkey: return "🦃"
        case .llama: return "🦙"
        }
    }
}

// MARK: - Animal Rarity Enum
enum AnimalRarity: String, CaseIterable, Codable {
    case common = "common"
    case rare = "rare"
    case epic = "epic"
    case legendary = "legendary"
    
    var displayName: String {
        return rawValue.capitalized
    }
}

// MARK: - Animal Helper Methods
extension Animal {
    
    // MARK: - Computed Properties
    var animalType: AnimalType {
        return AnimalType(rawValue: type ?? "cow") ?? .cow
    }
    
    var animalRarity: AnimalRarity {
        return AnimalRarity(rawValue: rarity ?? "common") ?? .common
    }
    
    var displayName: String {
        return animalType.displayName
    }
    
    var emoji: String {
        return animalType.emoji
    }
    
    // MARK: - Level Management
    var experienceToNextLevel: Int32 {
        // Note: experience property would need to be added to Core Data model
        let nextLevelXP = (self.level + 1) * 100 // 100 XP per level
        return nextLevelXP // Placeholder
    }

    var experienceProgress: Float {
        // Note: experience property would need to be added to Core Data model
        return 0.5 // Placeholder
    }
    
    // MARK: - Unlock Management
    func unlock() {
        isUnlocked = true
        unlockedAt = Date()
    }
    
    func lock() {
        isUnlocked = false
        unlockedAt = nil
    }
    
    // MARK: - Care Management
    func feed() {
        self.lastFedAt = Date()
        self.happiness = min(1.0, self.happiness + 0.1)

        // Note: experience property would need to be added to Core Data model
        print("🍎 Fed \(displayName)")
    }

    func play() {
        // Note: lastPlayedAt property would need to be added to Core Data model
        self.happiness = min(1.0, self.happiness + 0.15)

        print("🎾 Played with \(displayName)")
    }

    private func checkLevelUp() {
        // Note: experience property would need to be added to Core Data model
        print("🎉 \(displayName) could level up!")
    }
    
    // MARK: - Status Checks
    var needsFeeding: Bool {
        guard let lastFed = lastFedAt else { return true }
        let hoursSinceFeeding = Date().timeIntervalSince(lastFed) / 3600
        return hoursSinceFeeding > 12 // Needs feeding every 12 hours
    }
    
    var needsPlaying: Bool {
        // Note: lastPlayedAt property would need to be added to Core Data model
        return true // Placeholder
    }
    
    var isHappy: Bool {
        return happiness >= 0.7
    }
    
    // MARK: - Production
    var productionRate: Float {
        let baseRate: Float = 1.0
        let levelBonus = Float(level) * 0.1
        let happinessBonus = happiness * 0.5
        
        return baseRate + levelBonus + happinessBonus
    }
}
