// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		1783F2C92E37D75900B56893 /* SpriteKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 1783F2C82E37D75900B56893 /* SpriteKit.framework */; };
		1783F2CB2E37D75F00B56893 /* AVFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 1783F2CA2E37D75F00B56893 /* AVFoundation.framework */; };
		1783F2CD2E37D76300B56893 /* GameKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 1783F2CC2E37D76300B56893 /* GameKit.framework */; };
		1783F2CF2E37D76700B56893 /* CoreData.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 1783F2CE2E37D76700B56893 /* CoreData.framework */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		1783F2AB2E37D6FE00B56893 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 1783F2892E37D6FD00B56893 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 1783F2902E37D6FD00B56893;
			remoteInfo = Moofarm;
		};
		1783F2B52E37D6FE00B56893 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 1783F2892E37D6FD00B56893 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 1783F2902E37D6FD00B56893;
			remoteInfo = Moofarm;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		1783F2912E37D6FD00B56893 /* Moofarm.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Moofarm.app; sourceTree = BUILT_PRODUCTS_DIR; };
		1783F2AA2E37D6FE00B56893 /* MoofarmTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = MoofarmTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		1783F2B42E37D6FE00B56893 /* MoofarmUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = MoofarmUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		1783F2C82E37D75900B56893 /* SpriteKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = SpriteKit.framework; path = System/Library/Frameworks/SpriteKit.framework; sourceTree = SDKROOT; };
		1783F2CA2E37D75F00B56893 /* AVFoundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AVFoundation.framework; path = System/Library/Frameworks/AVFoundation.framework; sourceTree = SDKROOT; };
		1783F2CC2E37D76300B56893 /* GameKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = GameKit.framework; path = System/Library/Frameworks/GameKit.framework; sourceTree = SDKROOT; };
		1783F2CE2E37D76700B56893 /* CoreData.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreData.framework; path = System/Library/Frameworks/CoreData.framework; sourceTree = SDKROOT; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		1783F2932E37D6FD00B56893 /* Moofarm */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = Moofarm;
			sourceTree = "<group>";
		};
		1783F2AD2E37D6FE00B56893 /* MoofarmTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = MoofarmTests;
			sourceTree = "<group>";
		};
		1783F2B72E37D6FE00B56893 /* MoofarmUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = MoofarmUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		1783F28E2E37D6FD00B56893 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				1783F2CD2E37D76300B56893 /* GameKit.framework in Frameworks */,
				1783F2CF2E37D76700B56893 /* CoreData.framework in Frameworks */,
				1783F2CB2E37D75F00B56893 /* AVFoundation.framework in Frameworks */,
				1783F2C92E37D75900B56893 /* SpriteKit.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		1783F2A72E37D6FE00B56893 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		1783F2B12E37D6FE00B56893 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		1783F2882E37D6FD00B56893 = {
			isa = PBXGroup;
			children = (
				1783F2932E37D6FD00B56893 /* Moofarm */,
				1783F2AD2E37D6FE00B56893 /* MoofarmTests */,
				1783F2B72E37D6FE00B56893 /* MoofarmUITests */,
				1783F2C72E37D75900B56893 /* Frameworks */,
				1783F2922E37D6FD00B56893 /* Products */,
			);
			sourceTree = "<group>";
		};
		1783F2922E37D6FD00B56893 /* Products */ = {
			isa = PBXGroup;
			children = (
				1783F2912E37D6FD00B56893 /* Moofarm.app */,
				1783F2AA2E37D6FE00B56893 /* MoofarmTests.xctest */,
				1783F2B42E37D6FE00B56893 /* MoofarmUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		1783F2C72E37D75900B56893 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				1783F2CE2E37D76700B56893 /* CoreData.framework */,
				1783F2CC2E37D76300B56893 /* GameKit.framework */,
				1783F2CA2E37D75F00B56893 /* AVFoundation.framework */,
				1783F2C82E37D75900B56893 /* SpriteKit.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		1783F2902E37D6FD00B56893 /* Moofarm */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 1783F2BE2E37D6FE00B56893 /* Build configuration list for PBXNativeTarget "Moofarm" */;
			buildPhases = (
				1783F28D2E37D6FD00B56893 /* Sources */,
				1783F28E2E37D6FD00B56893 /* Frameworks */,
				1783F28F2E37D6FD00B56893 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				1783F2932E37D6FD00B56893 /* Moofarm */,
			);
			name = Moofarm;
			packageProductDependencies = (
			);
			productName = Moofarm;
			productReference = 1783F2912E37D6FD00B56893 /* Moofarm.app */;
			productType = "com.apple.product-type.application";
		};
		1783F2A92E37D6FE00B56893 /* MoofarmTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 1783F2C12E37D6FE00B56893 /* Build configuration list for PBXNativeTarget "MoofarmTests" */;
			buildPhases = (
				1783F2A62E37D6FE00B56893 /* Sources */,
				1783F2A72E37D6FE00B56893 /* Frameworks */,
				1783F2A82E37D6FE00B56893 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				1783F2AC2E37D6FE00B56893 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				1783F2AD2E37D6FE00B56893 /* MoofarmTests */,
			);
			name = MoofarmTests;
			packageProductDependencies = (
			);
			productName = MoofarmTests;
			productReference = 1783F2AA2E37D6FE00B56893 /* MoofarmTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		1783F2B32E37D6FE00B56893 /* MoofarmUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 1783F2C42E37D6FE00B56893 /* Build configuration list for PBXNativeTarget "MoofarmUITests" */;
			buildPhases = (
				1783F2B02E37D6FE00B56893 /* Sources */,
				1783F2B12E37D6FE00B56893 /* Frameworks */,
				1783F2B22E37D6FE00B56893 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				1783F2B62E37D6FE00B56893 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				1783F2B72E37D6FE00B56893 /* MoofarmUITests */,
			);
			name = MoofarmUITests;
			packageProductDependencies = (
			);
			productName = MoofarmUITests;
			productReference = 1783F2B42E37D6FE00B56893 /* MoofarmUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		1783F2892E37D6FD00B56893 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1620;
				LastUpgradeCheck = 1620;
				TargetAttributes = {
					1783F2902E37D6FD00B56893 = {
						CreatedOnToolsVersion = 16.2;
					};
					1783F2A92E37D6FE00B56893 = {
						CreatedOnToolsVersion = 16.2;
						TestTargetID = 1783F2902E37D6FD00B56893;
					};
					1783F2B32E37D6FE00B56893 = {
						CreatedOnToolsVersion = 16.2;
						TestTargetID = 1783F2902E37D6FD00B56893;
					};
				};
			};
			buildConfigurationList = 1783F28C2E37D6FD00B56893 /* Build configuration list for PBXProject "Moofarm" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 1783F2882E37D6FD00B56893;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = 1783F2922E37D6FD00B56893 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				1783F2902E37D6FD00B56893 /* Moofarm */,
				1783F2A92E37D6FE00B56893 /* MoofarmTests */,
				1783F2B32E37D6FE00B56893 /* MoofarmUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		1783F28F2E37D6FD00B56893 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		1783F2A82E37D6FE00B56893 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		1783F2B22E37D6FE00B56893 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		1783F28D2E37D6FD00B56893 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		1783F2A62E37D6FE00B56893 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		1783F2B02E37D6FE00B56893 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		1783F2AC2E37D6FE00B56893 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 1783F2902E37D6FD00B56893 /* Moofarm */;
			targetProxy = 1783F2AB2E37D6FE00B56893 /* PBXContainerItemProxy */;
		};
		1783F2B62E37D6FE00B56893 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 1783F2902E37D6FD00B56893 /* Moofarm */;
			targetProxy = 1783F2B52E37D6FE00B56893 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		1783F2BC2E37D6FE00B56893 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		1783F2BD2E37D6FE00B56893 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		1783F2BF2E37D6FE00B56893 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = VGQU7EVXZV;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.games";
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = Main;
				INFOPLIST_KEY_UIStatusBarHidden = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.tuanle.Moofarm;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		1783F2C02E37D6FE00B56893 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = VGQU7EVXZV;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.games";
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = Main;
				INFOPLIST_KEY_UIStatusBarHidden = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.tuanle.Moofarm;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		1783F2C22E37D6FE00B56893 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = VGQU7EVXZV;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.tuanle.MoofarmTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Moofarm.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Moofarm";
			};
			name = Debug;
		};
		1783F2C32E37D6FE00B56893 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = VGQU7EVXZV;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.tuanle.MoofarmTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Moofarm.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Moofarm";
			};
			name = Release;
		};
		1783F2C52E37D6FE00B56893 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = VGQU7EVXZV;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.tuanle.MoofarmUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = Moofarm;
			};
			name = Debug;
		};
		1783F2C62E37D6FE00B56893 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = VGQU7EVXZV;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.tuanle.MoofarmUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = Moofarm;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		1783F28C2E37D6FD00B56893 /* Build configuration list for PBXProject "Moofarm" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1783F2BC2E37D6FE00B56893 /* Debug */,
				1783F2BD2E37D6FE00B56893 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		1783F2BE2E37D6FE00B56893 /* Build configuration list for PBXNativeTarget "Moofarm" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1783F2BF2E37D6FE00B56893 /* Debug */,
				1783F2C02E37D6FE00B56893 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		1783F2C12E37D6FE00B56893 /* Build configuration list for PBXNativeTarget "MoofarmTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1783F2C22E37D6FE00B56893 /* Debug */,
				1783F2C32E37D6FE00B56893 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		1783F2C42E37D6FE00B56893 /* Build configuration list for PBXNativeTarget "MoofarmUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1783F2C52E37D6FE00B56893 /* Debug */,
				1783F2C62E37D6FE00B56893 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 1783F2892E37D6FD00B56893 /* Project object */;
}
