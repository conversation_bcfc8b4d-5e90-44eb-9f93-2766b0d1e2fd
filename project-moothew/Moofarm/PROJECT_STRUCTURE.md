# Project Moothew - Feature-Based Architecture
## Moo Farm: Music & Build - Project Structure

### 📁 **Current Project Structure**

```
MooFarm/
├── Moofarm/
│   ├── MoofarmApp.swift                    # ✅ Main app entry point with service injection
│   ├── ContentView.swift                   # ✅ Root view with navigation coordination
│   │
│   ├── Core/                              # ✅ Core architecture and shared components
│   │   ├── Architecture/
│   │   │   ├── BaseViewModel.swift         # ✅ Base class for all ViewModels
│   │   │   └── GameState.swift             # ✅ Game state management enums
│   │   ├── Data/
│   │   │   ├── Moofarm.xcdatamodeld       # ✅ Core Data model (moved from root)
│   │   │   └── Persistence.swift          # ✅ Core Data stack (to be renamed)
│   │   ├── Services/
│   │   │   ├── DataService.swift          # ✅ Core Data operations service
│   │   │   ├── AudioService.swift         # ✅ AVFoundation audio management
│   │   │   └── GameKitService.swift       # ✅ Game Center integration
│   │   └── Extensions/                    # 🔄 To be created
│   │
│   ├── Features/                          # ✅ Feature-based organization
│   │   ├── RhythmGame/                    # 🔄 To be implemented
│   │   │   ├── Scenes/                    # SpriteKit SKScenes
│   │   │   ├── Views/                     # SwiftUI container views
│   │   │   └── ViewModels/                # State management
│   │   ├── Farm/                          # 🔄 To be implemented
│   │   │   ├── Views/                     # SwiftUI farm interface
│   │   │   ├── ViewModels/                # Farm state management
│   │   │   └── Models/                    # Farm-specific models
│   │   ├── Menu/
│   │   │   ├── Views/
│   │   │   │   └── MainMenuView.swift     # ✅ Main menu with navigation
│   │   │   └── ViewModels/                # 🔄 To be created
│   │   └── Settings/                      # 🔄 To be implemented
│   │       ├── Views/
│   │       └── ViewModels/
│   │
│   ├── Resources/                         # 🔄 To be created
│   │   ├── Audio/                         # Music files, sound effects
│   │   ├── Sprites/                       # Game sprites, textures
│   │   └── Fonts/                         # Custom fonts
│   │
│   ├── Assets.xcassets                    # ✅ Asset catalog
│   └── Preview Content/                   # ✅ SwiftUI previews
│
├── MoofarmTests/                          # ✅ Unit tests
└── MoofarmUITests/                        # ✅ UI tests
```

---

### ✅ **Completed Components**

#### **1. Core Architecture**
- **✅ BaseViewModel.swift** - MVVM base class với Combine support
- **✅ GameState.swift** - Game state management enums
- **✅ DataService.swift** - Core Data operations với Combine
- **✅ AudioService.swift** - AVFoundation audio management
- **✅ GameKitService.swift** - Game Center integration

#### **2. App Foundation**
- **✅ MoofarmApp.swift** - Service injection và app lifecycle
- **✅ ContentView.swift** - Navigation coordination
- **✅ MainMenuView.swift** - Main menu với placeholder navigation

#### **3. Service Integration**
- **✅ Environment injection** cho tất cả services
- **✅ Combine-based** reactive architecture
- **✅ Error handling** và loading states
- **✅ GameKit authentication** flow

---

### 🔄 **Next Implementation Steps**

#### **Phase 1: Complete Foundation (Week 1-2)**
1. **Rename Persistence.swift** → **CoreDataStack.swift**
2. **Create Extensions directory** với Swift extensions
3. **Add SwiftLint configuration**
4. **Create Resources directories** và placeholder assets
5. **Setup Core Data entities** theo PRD requirements

#### **Phase 2: Rhythm Game Core (Week 3-6)**
1. **Create RhythmGameScene.swift** (SpriteKit)
2. **Implement note spawning system**
3. **Add touch detection và scoring**
4. **Integrate với AudioService** cho beat detection
5. **Create RhythmGameView.swift** (SwiftUI container)

#### **Phase 3: Farm System (Week 7-10)**
1. **Create FarmView.swift** với grid system
2. **Implement building placement** logic
3. **Add animal collection** system
4. **Create farm-rhythm integration**
5. **Implement save/load** functionality

---

### 🏗️ **Architecture Benefits**

#### **✅ Separation of Concerns**
- **Core**: Shared architecture và services
- **Features**: Isolated feature modules
- **Resources**: Centralized assets

#### **✅ Scalability**
- Easy to add new features
- Clear dependency management
- Modular testing approach

#### **✅ Maintainability**
- Feature-based organization
- Consistent MVVM pattern
- Reactive data flow với Combine

#### **✅ Team Collaboration**
- Clear ownership boundaries
- Parallel development possible
- Consistent code structure

---

### 📋 **Development Guidelines**

#### **File Naming Conventions**
- **Views**: `[Feature][Purpose]View.swift` (e.g., `RhythmGameView.swift`)
- **ViewModels**: `[Feature][Purpose]ViewModel.swift` (e.g., `FarmViewModel.swift`)
- **Services**: `[Purpose]Service.swift` (e.g., `AudioService.swift`)
- **Models**: `[Entity]Model.swift` hoặc Core Data entities

#### **Code Organization**
- **MVVM pattern** cho tất cả features
- **Combine publishers** cho reactive data flow
- **Environment injection** cho services
- **Protocol-based** service definitions

#### **Testing Strategy**
- **Unit tests** cho ViewModels và Services
- **Integration tests** cho Core Data operations
- **UI tests** cho critical user flows
- **SpriteKit tests** cho rhythm game mechanics

---

### 🎯 **Current Status**

**✅ COMPLETED:**
- Project restructuring theo feature-based architecture
- Core services implementation (Data, Audio, GameKit)
- Base MVVM architecture với Combine
- Main menu với navigation placeholders
- Service injection và environment setup

**🔄 NEXT PRIORITIES:**
1. Complete Core Data entity definitions
2. Implement RhythmGame SpriteKit scenes
3. Create Farm building system
4. Add cross-system integration
5. Implement monetization features

**📊 PROGRESS:** Foundation Phase ~80% Complete

---

### 🚀 **Ready for Development**

The project is now properly structured với:
- ✅ **Feature-based architecture** implemented
- ✅ **MVVM + Combine** pattern established
- ✅ **Service layer** created và integrated
- ✅ **Navigation foundation** ready
- ✅ **Development guidelines** defined

**Next step:** Begin implementing Core Data entities và RhythmGame SpriteKit scenes according to PRD specifications.

---

**Document Version:** 1.0  
**Last Updated:** January 28, 2025  
**Status:** Architecture Complete - Ready for Feature Implementation
