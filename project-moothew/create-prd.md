# Product Requirements Document (PRD)
## Moo Farm: Music & Build - Nông Trại Âm Nhạc

### 📋 **Document Information**
- **Product Name:** Moo Farm: Music & Build - Nông Trại Âm Nhạc
- **Project Codename:** Project Moothew
- **Document Version:** 1.0
- **Last Updated:** January 2025
- **Product Manager:** [Your Name]
- **Target Platform:** iOS 17+ (iPhone/iPad)

---

## 🎯 **Executive Summary**

### **Vision Statement**
Tạo ra một trải nghiệm game mobile độc đáo kết hợp giữa Rhythm Game và Farm Simulation, tận dụng trend "Moothew" để thu hút Gen Z Việt Nam và xây dựng một cộng đồng game thủ gắn bó lâu dài.

### **Product Overview**
Moo Farm là game Hybrid-casual với hai gameplay loop chính:
1. **Rhythm Game:** Người chơi chạm đúng nhịp các nốt nhạc hình động vật để kiếm tài nguyên
2. **Farm Simulation:** Sử dụng tài nguyên để xây dựng, trang trí nông trại và sưu tầm "nhạc công" động vật

### **Target Market**
- **Primary:** Gen Z Việt Nam (18-25 tuổi)
- **Secondary:** Millennials trẻ (26-30 tuổi) yêu thích game casual
- **Market Size:** 54.6 triệu game thủ mobile tại Việt Nam

---

## 🎮 **Product Goals & Objectives**

### **Business Goals**
1. **Revenue Target:** $500K ARR trong năm đầu
2. **User Acquisition:** 100K downloads trong 3 tháng đầu
3. **Retention:** D1 >40%, D7 >20%, D30 >10%
4. **Monetization:** ARPU $0.50-1.00, IAP conversion rate >5%

### **Product Goals**
1. Tạo ra gameplay loop gây nghiện và bền vững
2. Xây dựng hệ sinh thái nội dung phong phú
3. Phát triển cộng đồng người chơi tích cực
4. Thiết lập nền tảng cho các update và expansion

### **User Goals**
1. **Entertainment:** Trải nghiệm giải trí vui nhộn, thư giãn
2. **Achievement:** Cảm giác thành tựu qua progression system
3. **Social Connection:** Kết nối với bạn bè qua leaderboards và challenges
4. **Self-Expression:** Thể hiện cá tính qua farm customization

---

## 👥 **Target Audience**

### **Primary Persona: "Minh - Gen Z Gamer"**
- **Demographics:** Nam, 22 tuổi, sinh viên/nhân viên văn phòng
- **Behavior:** Chơi game mobile 1-2 giờ/ngày, active trên TikTok
- **Motivations:** Giải trí, giải tỏa stress, thể hiện bản thân
- **Pain Points:** Game quá phức tạp hoặc quá đơn giản, thiếu nội dung mới
- **Spending:** Sẵn sàng chi $2-5/tháng cho game yêu thích

### **Secondary Persona: "Linh - Casual Player"**
- **Demographics:** Nữ, 26 tuổi, nhân viên marketing
- **Behavior:** Chơi game trong lúc nghỉ trưa, cuối tuần
- **Motivations:** Thư giãn, sưu tầm, trang trí
- **Pain Points:** Không có thời gian cho game hardcore
- **Spending:** Chi tiêu thận trọng, thích free-to-play với IAP nhỏ

---

## 🎯 **Core Features & Requirements**

### **MVP Features (Must Have)**

#### **1. Rhythm Game Core**
- **User Story:** "Là người chơi, tôi muốn chạm đúng nhịp các nốt nhạc để kiếm điểm và tài nguyên"
- **Acceptance Criteria:**
  - [ ] Hỗ trợ 4 lanes với touch detection chính xác
  - [ ] Hệ thống scoring: Perfect (100pts), Good (75pts), Okay (50pts), Miss (0pts)
  - [ ] Combo system với multiplier tối đa x3
  - [ ] 3 mức độ khó: Easy, Normal, Hard
  - [ ] Visual feedback cho mỗi hit (particles, screen shake)
  - [ ] Audio sync với độ trễ <50ms

#### **2. Farm Building System**
- **User Story:** "Là người chơi, tôi muốn xây dựng và trang trí nông trại của mình"
- **Acceptance Criteria:**
  - [ ] Grid-based building system với drag & drop
  - [ ] 5 loại building cơ bản: Barn, House, Coop, Pond, Decoration
  - [ ] Building upgrade system (3 levels mỗi building)
  - [ ] Resource management: Coins, Wood, Stone
  - [ ] Save/Load farm layout tự động

#### **3. Animal Collection**
- **User Story:** "Là người chơi, tôi muốn sưu tầm các loài động vật để mở khóa nhạc mới"
- **Acceptance Criteria:**
  - [ ] 12 loài động vật cơ bản: Cow, Pig, Chicken, Sheep, Duck, Goat, etc.
  - [ ] 4 mức độ hiếm: Common, Rare, Epic, Legendary
  - [ ] Animal care system: Feeding, Happiness meter
  - [ ] Collection book với progress tracking
  - [ ] Mỗi animal unlock tạo ra 1 bài hát mới

#### **4. Cross-System Integration**
- **User Story:** "Là người chơi, tôi muốn thấy sự liên kết giữa rhythm game và farm"
- **Acceptance Criteria:**
  - [ ] Rhythm game performance ảnh hưởng đến animal unlock rate
  - [ ] Farm happiness bonus cho rhythm game scoring
  - [ ] Daily tasks kết hợp cả hai gameplay
  - [ ] Shared progression system

### **Phase 2 Features (Should Have)**

#### **5. Battle Pass System**
- **User Story:** "Là người chơi, tôi muốn có mục tiêu dài hạn và rewards độc quyền"
- **Acceptance Criteria:**
  - [ ] Seasonal Battle Pass (60 days/season)
  - [ ] Free track với 30 tiers
  - [ ] Premium track với 60 tiers và exclusive rewards
  - [ ] XP earning từ cả rhythm game và farm activities
  - [ ] Premium purchase flow ($4.99/season)

#### **6. Social Features**
- **User Story:** "Là người chơi, tôi muốn cạnh tranh và chia sẻ với bạn bè"
- **Acceptance Criteria:**
  - [ ] GameKit integration với Game Center
  - [ ] Leaderboards: Weekly High Scores, Farm Ratings
  - [ ] Achievement system với 50+ achievements
  - [ ] Friend challenges và farm visiting
  - [ ] Social sharing cho major milestones

### **Phase 3 Features (Could Have)**

#### **7. Advanced Monetization**
- **User Story:** "Là người chơi, tôi muốn customize và express bản thân"
- **Acceptance Criteria:**
  - [ ] Cosmetic store với 100+ items
  - [ ] Premium currency (Gems) system
  - [ ] Rewarded ads cho bonus rewards
  - [ ] Limited-time offers và bundles
  - [ ] Brand collaboration items

#### **8. Live Operations**
- **User Story:** "Là người chơi, tôi muốn có nội dung mới thường xuyên"
- **Acceptance Criteria:**
  - [ ] Weekly events với special rewards
  - [ ] Seasonal themes (Tết, Halloween, Christmas)
  - [ ] Limited-time animals và buildings
  - [ ] Community challenges
  - [ ] Push notifications cho events

---

## 🏗️ **Technical Requirements**

### **Platform Specifications**
- **Minimum iOS Version:** iOS 17.0
- **Supported Devices:** iPhone SE (3rd gen) to iPhone 15 Pro Max, iPad (9th gen) to iPad Pro
- **Development Framework:** SwiftUI + SpriteKit
- **Architecture:** MVVM + Combine
- **Data Storage:** Core Data + iCloud sync
- **Audio:** AVFoundation với real-time mixing

### **Performance Requirements**
- **Frame Rate:** 60 FPS stable gameplay
- **Load Time:** <3 seconds app launch, <2 seconds scene transitions
- **Memory Usage:** <150MB peak usage
- **Battery:** <5% drain per 30-minute session
- **Crash Rate:** <0.5% crash-free sessions

### **Security & Privacy**
- **Data Protection:** All user data encrypted at rest
- **Privacy Compliance:** GDPR, CCPA compliant
- **Child Safety:** COPPA compliant (no data collection <13 years)
- **GameKit:** Secure authentication và anti-cheat measures

---

## 💰 **Monetization Strategy**

### **Revenue Streams**
1. **Battle Pass (40% revenue):** $4.99/season, 4 seasons/year
2. **In-App Purchases (35% revenue):** Cosmetics, premium currency
3. **Rewarded Ads (20% revenue):** Optional bonus rewards
4. **Brand Partnerships (5% revenue):** Sponsored content và items

### **Pricing Strategy**
- **Battle Pass:** $4.99 (competitive với market standards)
- **Gem Packages:** $0.99 (100 gems), $4.99 (600 gems), $9.99 (1300 gems)
- **Cosmetic Items:** $0.99-2.99 per item
- **Remove Ads:** $2.99 one-time purchase

### **Conversion Funnel**
1. **Free Player (100%):** Enjoy core gameplay
2. **Engaged Player (30%):** Complete tutorial, play >3 days
3. **Paying Player (5%):** Make first purchase
4. **Whale Player (1%):** Spend >$20/month

---

## 📊 **Success Metrics & KPIs**

### **Acquisition Metrics**
- **Downloads:** 100K in first 3 months
- **Install Rate:** >3% from ads
- **Cost Per Install (CPI):** <$2.00
- **Organic vs Paid:** 60% organic, 40% paid

### **Engagement Metrics**
- **Day 1 Retention:** >40%
- **Day 7 Retention:** >20%
- **Day 30 Retention:** >10%
- **Session Length:** 5-10 minutes average
- **Sessions per Day:** 3-5 sessions

### **Monetization Metrics**
- **ARPU (Average Revenue Per User):** $0.50-1.00
- **ARPPU (Average Revenue Per Paying User):** $15-25
- **IAP Conversion Rate:** >5%
- **Battle Pass Conversion:** >10%
- **LTV (Lifetime Value):** >$5.00

### **Quality Metrics**
- **App Store Rating:** >4.5 stars
- **Crash-Free Sessions:** >99.5%
- **Load Time:** <3 seconds
- **Customer Support Response:** <24 hours

---

## 🚀 **Go-to-Market Strategy**

### **Pre-Launch (Weeks 13-15)**
- **Soft Launch:** Vietnam market only
- **Beta Testing:** 1000 TestFlight users
- **Influencer Outreach:** 10 gaming KOCs
- **App Store Optimization:** Keywords, screenshots, description

### **Launch (Week 16)**
- **TikTok Campaign:** $10K budget, trend-based creative
- **PR Campaign:** Gaming media outreach
- **Community Building:** Discord server, Facebook group
- **Launch Event:** Virtual launch party với prizes

### **Post-Launch (Weeks 17-20)**
- **User Feedback Integration:** Weekly updates based on reviews
- **Content Updates:** New animals, songs, buildings monthly
- **Seasonal Events:** Tết event, Valentine's event
- **Expansion Planning:** Android version, new markets

---

## 🎨 **Design Requirements**

### **Visual Style**
- **Art Direction:** Cute, colorful, 2D cartoon style
- **Color Palette:** Warm pastels (peach, mint, lavender) với bright accents
- **Typography:** Rounded, friendly fonts (SF Pro Rounded)
- **UI Elements:** Soft shadows, rounded corners, gentle animations

### **Audio Design**
- **Music Style:** Upbeat, farm-themed melodies với Vietnamese influences
- **Sound Effects:** High-quality animal sounds, satisfying UI feedback
- **Voice Acting:** Optional "moothew" samples, cheerful narrator
- **Audio Settings:** Separate volume controls for music/SFX

### **User Experience**
- **Onboarding:** 3-step tutorial covering both gameplay loops
- **Navigation:** Tab-based với clear visual hierarchy
- **Accessibility:** VoiceOver support, Dynamic Type, high contrast mode
- **Localization:** Vietnamese và English support

---

## ⚠️ **Risks & Mitigation**

### **Technical Risks**
- **Risk:** SwiftUI performance issues on older devices
- **Mitigation:** Extensive device testing, fallback UI components

- **Risk:** Audio sync problems in rhythm game
- **Mitigation:** Early audio system prototyping, latency testing

### **Market Risks**
- **Risk:** Trend "Moothew" loses popularity
- **Mitigation:** Build sustainable gameplay beyond trend, adaptable content system

- **Risk:** High competition in casual game market
- **Mitigation:** Unique hybrid gameplay, strong community focus

### **Business Risks**
- **Risk:** Low monetization conversion
- **Mitigation:** A/B testing pricing, value-focused IAP design

- **Risk:** Development timeline delays
- **Mitigation:** Agile development, MVP-first approach, regular milestone reviews

---

## 📅 **Timeline & Milestones**

### **Development Phases**
- **Phase 1 (Weeks 1-3):** Foundation & Architecture
- **Phase 2 (Weeks 4-7):** Rhythm Game Core
- **Phase 3 (Weeks 8-11):** Farm Simulation
- **Phase 4 (Weeks 12-14):** Monetization & Social
- **Phase 5 (Weeks 15-16):** Polish & Launch

### **Key Milestones**
- **Week 4:** Playable rhythm game prototype
- **Week 8:** Integrated rhythm + farm demo
- **Week 12:** Feature-complete alpha build
- **Week 15:** Release candidate ready
- **Week 16:** App Store launch

---

## ✅ **Acceptance Criteria**

### **Launch Readiness Checklist**
- [ ] All MVP features implemented và tested
- [ ] Performance benchmarks met on target devices
- [ ] App Store review guidelines compliance
- [ ] Privacy policy và legal documents complete
- [ ] Analytics và crash reporting integrated
- [ ] Customer support system ready
- [ ] Marketing materials và campaigns prepared

### **Success Definition**
The product will be considered successful if it achieves:
- 50K+ downloads in first month
- 4.0+ App Store rating
- $10K+ revenue in first quarter
- 15%+ D7 retention rate
- Positive user feedback và community growth

---

## 📋 **Non-Goals (Out of Scope)**

### **What We Will NOT Build in MVP**
- **Android Version:** Focus on iOS first, Android later
- **Multiplayer Real-time:** Async social features only
- **3D Graphics:** Keep 2D for performance và development speed
- **Voice Chat:** Text-based communication only
- **Blockchain/NFT:** Traditional monetization model
- **Complex Crafting:** Simple resource management only

---

## 🔄 **Future Roadmap (Post-Launch)**

### **Version 1.1 (Month 2-3)**
- New animal types và songs
- Seasonal events system
- Performance optimizations
- Bug fixes based on user feedback

### **Version 1.2 (Month 4-6)**
- Guild/Clan system
- Advanced farm customization
- New building types
- Android version development

### **Version 2.0 (Month 7-12)**
- New game mode (possibly PvP rhythm battles)
- Advanced social features
- International market expansion
- Brand partnership integrations

---

**Document Approval:**
- Product Manager: ________________
- Engineering Lead: ________________
- Design Lead: ________________
- Business Stakeholder: ________________

**Document Status:** Ready for Development - Version 1.0