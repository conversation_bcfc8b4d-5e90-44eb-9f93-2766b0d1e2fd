# Project Moothew - Technical Specifications
## SwiftUI + SpriteKit Hybrid Architecture

### 🏗️ **Architecture Overview**

#### **MVVM + Combine Pattern**
```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   SwiftUI View  │◄──►│   ViewModel      │◄──►│     Model       │
│                 │    │ (ObservableObject)│    │ (Core Data)     │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│ SpriteKit Scene │    │ Combine Pipeline │    │   Services      │
│   (Rhythm Game) │    │   (@Published)   │    │ (Audio/GameKit) │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

### **Core Components**

#### **1. Game State Management**
```swift
class GameViewModel: ObservableObject {
    @Published var gameState: GameState = .menu
    @Published var playerStats: PlayerStats
    @Published var currentSong: Song?
    @Published var farmData: FarmData
    
    private let dataService: DataService
    private let audioService: AudioService
    private var cancellables = Set<AnyCancellable>()
}

enum GameState {
    case menu, rhythmGame, farm, settings, loading
}
```

#### **2. Rhythm Game Engine (SpriteKit)**
```swift
class RhythmGameScene: SKScene {
    // Core game objects
    private var noteSpawner: NoteSpawner
    private var scoreManager: ScoreManager
    private var beatDetector: BeatDetector
    
    // Game configuration
    private var songData: SongData
    private var difficulty: Difficulty
    private var gameSpeed: Float = 1.0
    
    override func update(_ currentTime: TimeInterval) {
        noteSpawner.update(currentTime)
        beatDetector.update(currentTime)
        handleInput()
    }
}
```

#### **3. Farm System (SwiftUI)**
```swift
struct FarmView: View {
    @StateObject private var farmViewModel: FarmViewModel
    @State private var selectedBuilding: Building?
    @State private var isDragging = false
    
    var body: some View {
        ZStack {
            FarmGridView(farm: farmViewModel.farm)
            BuildingPalette()
            ResourceBar()
        }
        .onDrop(of: [.text], delegate: BuildingDropDelegate())
    }
}
```

---

### 🎵 **Audio System Architecture**

#### **AVFoundation Integration**
```swift
class AudioService: ObservableObject {
    private var musicPlayer: AVAudioPlayer?
    private var sfxPlayers: [String: AVAudioPlayer] = [:]
    private var audioEngine: AVAudioEngine
    private var beatTimer: Timer?
    
    // Real-time beat detection
    func startBeatDetection(for song: Song) {
        let bpm = song.beatsPerMinute
        let interval = 60.0 / Double(bpm)
        
        beatTimer = Timer.scheduledTimer(withTimeInterval: interval, repeats: true) { _ in
            NotificationCenter.default.post(name: .beatDetected, object: nil)
        }
    }
    
    // Dynamic audio mixing
    func playNoteSound(_ animalType: AnimalType, volume: Float = 1.0) {
        let soundName = "\(animalType.rawValue)_sound"
        sfxPlayers[soundName]?.setVolume(volume, fadeDuration: 0.1)
        sfxPlayers[soundName]?.play()
    }
}
```

#### **Beat Synchronization System**
```swift
class BeatDetector: ObservableObject {
    @Published var currentBeat: Int = 0
    @Published var isOnBeat: Bool = false
    
    private var songStartTime: TimeInterval = 0
    private var bpm: Double = 120
    private let tolerance: TimeInterval = 0.1
    
    func checkBeatAccuracy(tapTime: TimeInterval) -> HitAccuracy {
        let expectedBeatTime = calculateExpectedBeatTime()
        let timeDifference = abs(tapTime - expectedBeatTime)
        
        switch timeDifference {
        case 0...0.05: return .perfect
        case 0.05...0.1: return .good
        case 0.1...0.15: return .okay
        default: return .miss
        }
    }
}
```

---

### 🗄️ **Data Layer (Core Data)**

#### **Entity Relationships**
```swift
// Player Entity
@objc(Player)
class Player: NSManagedObject {
    @NSManaged var level: Int32
    @NSManaged var experience: Int32
    @NSManaged var coins: Int32
    @NSManaged var gems: Int32
    @NSManaged var farm: Farm?
    @NSManaged var unlockedAnimals: Set<Animal>
    @NSManaged var songProgress: Set<SongProgress>
}

// Farm Entity
@objc(Farm)
class Farm: NSManagedObject {
    @NSManaged var layoutData: Data // JSON serialized grid
    @NSManaged var buildings: Set<Building>
    @NSManaged var decorations: Set<Decoration>
    @NSManaged var animals: Set<Animal>
    @NSManaged var lastUpdated: Date
}

// Animal Entity (Nhạc công)
@objc(Animal)
class Animal: NSManagedObject {
    @NSManaged var type: String // cow, pig, chicken, etc.
    @NSManaged var rarity: String // common, rare, epic, legendary
    @NSManaged var level: Int32
    @NSManaged var happiness: Float
    @NSManaged var soundVariant: String
    @NSManaged var isUnlocked: Bool
}
```

#### **Data Service Layer**
```swift
class DataService: ObservableObject {
    lazy var persistentContainer: NSPersistentContainer = {
        let container = NSPersistentContainer(name: "MooFarm")
        container.loadPersistentStores { _, error in
            if let error = error {
                fatalError("Core Data error: \(error)")
            }
        }
        return container
    }()
    
    // CRUD Operations
    func savePlayer(_ player: Player) -> AnyPublisher<Void, Error> {
        Future { promise in
            self.persistentContainer.performBackgroundTask { context in
                do {
                    try context.save()
                    promise(.success(()))
                } catch {
                    promise(.failure(error))
                }
            }
        }
        .eraseToAnyPublisher()
    }
    
    func fetchAnimals() -> AnyPublisher<[Animal], Error> {
        let request: NSFetchRequest<Animal> = Animal.fetchRequest()
        request.predicate = NSPredicate(format: "isUnlocked == YES")
        
        return Future { promise in
            do {
                let animals = try self.persistentContainer.viewContext.fetch(request)
                promise(.success(animals))
            } catch {
                promise(.failure(error))
            }
        }
        .eraseToAnyPublisher()
    }
}
```

---

### 🎮 **Game Logic Systems**

#### **Scoring System**
```swift
class ScoreManager: ObservableObject {
    @Published var currentScore: Int = 0
    @Published var combo: Int = 0
    @Published var multiplier: Float = 1.0
    
    private let baseScores: [HitAccuracy: Int] = [
        .perfect: 100,
        .good: 75,
        .okay: 50,
        .miss: 0
    ]
    
    func processHit(_ accuracy: HitAccuracy) {
        let baseScore = baseScores[accuracy] ?? 0
        let finalScore = Int(Float(baseScore) * multiplier)
        
        currentScore += finalScore
        
        if accuracy != .miss {
            combo += 1
            updateMultiplier()
        } else {
            combo = 0
            multiplier = 1.0
        }
        
        // Trigger visual feedback
        NotificationCenter.default.post(
            name: .scoreUpdated,
            object: ScoreUpdate(score: finalScore, accuracy: accuracy)
        )
    }
    
    private func updateMultiplier() {
        multiplier = min(3.0, 1.0 + Float(combo) * 0.1)
    }
}
```

#### **Animal Collection System**
```swift
class AnimalCollectionManager: ObservableObject {
    @Published var availableAnimals: [AnimalType] = []
    @Published var collectedAnimals: Set<Animal> = []
    
    func unlockAnimal(type: AnimalType, rarity: Rarity) -> Animal? {
        let animal = Animal(context: dataService.context)
        animal.type = type.rawValue
        animal.rarity = rarity.rawValue
        animal.level = 1
        animal.happiness = 1.0
        animal.isUnlocked = true
        animal.soundVariant = generateSoundVariant(type: type, rarity: rarity)
        
        collectedAnimals.insert(animal)
        
        // Generate new song for this animal
        let newSong = generateSongForAnimal(animal)
        NotificationCenter.default.post(name: .newSongUnlocked, object: newSong)
        
        return animal
    }
    
    private func generateSongForAnimal(_ animal: Animal) -> Song {
        // Algorithm to create unique melody based on animal characteristics
        let melody = MelodyGenerator.generate(
            baseSound: animal.soundVariant,
            complexity: animal.rarity.complexityLevel,
            tempo: BPMRange.forRarity(animal.rarity)
        )
        
        return Song(melody: melody, animal: animal)
    }
}
```

---

### 🔄 **State Management with Combine**

#### **Reactive Data Flow**
```swift
class GameCoordinator: ObservableObject {
    @Published var currentScreen: Screen = .mainMenu
    @Published var isLoading: Bool = false
    @Published var errorMessage: String?
    
    private let gameViewModel: GameViewModel
    private let farmViewModel: FarmViewModel
    private let rhythmViewModel: RhythmGameViewModel
    private var cancellables = Set<AnyCancellable>()
    
    init() {
        setupBindings()
    }
    
    private func setupBindings() {
        // Cross-system data flow
        rhythmViewModel.$gameResult
            .compactMap { $0 }
            .sink { [weak self] result in
                self?.farmViewModel.processRhythmGameReward(result)
            }
            .store(in: &cancellables)
        
        farmViewModel.$newAnimalUnlocked
            .compactMap { $0 }
            .sink { [weak self] animal in
                self?.rhythmViewModel.addNewSong(for: animal)
            }
            .store(in: &cancellables)
    }
}
```

#### **Performance Optimization**
```swift
// Debounced updates for expensive operations
extension Publisher {
    func debounceAndSave<T: NSManagedObject>(
        _ object: T,
        using dataService: DataService
    ) -> AnyPublisher<Void, Error> {
        self
            .debounce(for: .seconds(1), scheduler: DispatchQueue.main)
            .flatMap { _ in
                dataService.save(object)
            }
            .eraseToAnyPublisher()
    }
}

// Memory management for SpriteKit
class NodePool<T: SKNode> {
    private var availableNodes: [T] = []
    private let createNode: () -> T
    
    init(initialSize: Int, createNode: @escaping () -> T) {
        self.createNode = createNode
        for _ in 0..<initialSize {
            availableNodes.append(createNode())
        }
    }
    
    func getNode() -> T {
        if let node = availableNodes.popLast() {
            return node
        } else {
            return createNode()
        }
    }
    
    func returnNode(_ node: T) {
        node.removeFromParent()
        node.removeAllActions()
        availableNodes.append(node)
    }
}
```

---

### 📱 **Platform Integration**

#### **GameKit Integration**
```swift
class GameKitService: NSObject, ObservableObject {
    @Published var isAuthenticated = false
    @Published var localPlayer: GKLocalPlayer?
    
    func authenticatePlayer() {
        GKLocalPlayer.local.authenticateHandler = { [weak self] viewController, error in
            if let viewController = viewController {
                // Present authentication UI
                self?.presentAuthenticationViewController(viewController)
            } else if GKLocalPlayer.local.isAuthenticated {
                self?.isAuthenticated = true
                self?.localPlayer = GKLocalPlayer.local
                self?.loadAchievements()
            }
        }
    }
    
    func submitScore(_ score: Int, category: String) {
        let scoreReporter = GKScore(leaderboardIdentifier: category)
        scoreReporter.value = Int64(score)
        
        GKScore.report([scoreReporter]) { error in
            if let error = error {
                print("Score submission error: \(error)")
            }
        }
    }
}
```

#### **In-App Purchases**
```swift
class StoreKitService: NSObject, ObservableObject {
    @Published var products: [SKProduct] = []
    @Published var purchaseInProgress = false
    
    private let productIdentifiers: Set<String> = [
        "com.moofarm.battlepass.premium",
        "com.moofarm.coins.small",
        "com.moofarm.coins.large",
        "com.moofarm.remove_ads"
    ]
    
    func purchaseProduct(_ product: SKProduct) {
        guard SKPaymentQueue.canMakePayments() else { return }
        
        purchaseInProgress = true
        let payment = SKPayment(product: product)
        SKPaymentQueue.default().add(payment)
    }
}
```

---

### 🚀 **Performance Considerations**

#### **Memory Management**
- Object pooling cho SpriteKit nodes
- Lazy loading cho farm assets
- Background context cho Core Data operations
- Texture atlases cho sprite optimization

#### **Battery Optimization**
- Frame rate limiting khi không active
- Audio session management
- Background task completion
- Efficient animation curves

#### **Network Efficiency**
- Batch GameKit operations
- Compress save data
- Offline-first architecture
- Smart sync strategies

---

**Next:** Implementation begins with Phase 1 setup và architecture foundation.
